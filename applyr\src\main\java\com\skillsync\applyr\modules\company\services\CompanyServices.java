package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.authentication.models.ChangePassDTO;
import com.skillsync.applyr.modules.authentication.models.RegisterRequestDTO;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.models.QualificationUpdateDTO;
import com.skillsync.applyr.modules.company.models.BulkImportResultDTO;
import com.skillsync.applyr.modules.sales.models.QuoteRequestDTO;
import com.skillsync.applyr.modules.sales.models.RaiseQuoteAndInvoiceDTO;
import com.skillsync.applyr.modules.sales.models.RequestStatusChangeDTO;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class CompanyServices {

    private final QualificationService qualificationService;
    private final RegistrationService registrationService;
    private final EmployeeProfileService employeeProfileService;
    private final LeadsService leadsService;
    private final ApplicationService applicationService;
    private final DashboardService dashboardService;
    private final TrackerService trackerService;
    private final TargetService targetService;
    private final QuoteInvoiceService quoteInvoiceService;
    private final MigrationService migrationService;
    private final XeroDataService xeroDataService;

    public CompanyServices(QualificationService qualificationService,
                           RegistrationService registrationService,
                           EmployeeProfileService employeeProfileService,
                           LeadsService leadsService,
                           ApplicationService applicationService,
                           DashboardService dashboardService,
                           TrackerService trackerService,
                           TargetService targetService,
                           QuoteInvoiceService quoteInvoiceService,
                           MigrationService migrationService,
                           XeroDataService xeroDataService) {
        this.qualificationService = qualificationService;
        this.registrationService = registrationService;
        this.employeeProfileService = employeeProfileService;
        this.leadsService = leadsService;
        this.applicationService = applicationService;
        this.dashboardService = dashboardService;
        this.trackerService = trackerService;
        this.targetService = targetService;
        this.quoteInvoiceService = quoteInvoiceService;
        this.migrationService = migrationService;
        this.xeroDataService = xeroDataService;
    }


    public SuccessResponse addQualification(QualificationRequestDTO qualification) {
        return qualificationService.addQualification(qualification);
    }

    public SuccessResponse updateQualification(QualificationUpdateDTO qualificationUpdateDTO) {
        return qualificationService.updateQualification(qualificationUpdateDTO);
    }

    public SuccessResponse deleteQualification(String qualificationId) {
        return qualificationService.deleteQualification(qualificationId);
    }

    public BulkImportResultDTO bulkImportQualifications(MultipartFile file) {
        return qualificationService.bulkImportQualifications(file);
    }

    public SuccessResponse registerAdmin(AdminRegisterDTO adminInfo) {
        return registrationService.registerAdmin(adminInfo);
    }

    public SuccessResponse setup() {
        AdminRegisterDTO adminRegisterDTO = new AdminRegisterDTO();
        adminRegisterDTO.setFullName("System Admin");
        adminRegisterDTO.setEmail("admin");
        adminRegisterDTO.setGender("Male");
        adminRegisterDTO.setPhone("01191421477");
        adminRegisterDTO.setAddress("Dhaka");

        RegisterRequestDTO login = new RegisterRequestDTO("admin", "", com.skillsync.applyr.core.models.enums.Roles.ADMIN);
        adminRegisterDTO.setRegisterRequestDTO(login);
        return registerAdmin(adminRegisterDTO);
    }

    public List<ProfileDTO> getAllEmployees() {
        return employeeProfileService.getAllEmployees();
    }

    public ProfileDTO findProfileFromUsername(String username) {
        return employeeProfileService.findProfileFromUsername(username);
    }

    public ProfileDTO getProfile() {
        return employeeProfileService.getProfile();
    }

    public SuccessResponse registerOperations(AdminRegisterDTO info) {
        return registrationService.registerOperations(info);
    }

    public SuccessResponse registerSalesAgent(AdminRegisterDTO salesInfo) {
        return registrationService.registerSalesAgent(salesInfo);
    }

    public ProfileDTO getAgentProfile() {
        return employeeProfileService.getAgentProfile();
    }

    public ProfileDTO getAgentProfileByUsername(String username) {
        return employeeProfileService.getAgentProfileByUsername(username);
    }

    public DashboardDTO getAgentDashboard(LocalDateTime startDate, LocalDateTime endDate) {
        return dashboardService.getAgentDashboard(startDate, endDate);
    }

    public DashboardDTO getAgentDashboardByTargetId(Long targetId) {
        return dashboardService.getAgentDashboardByTargetId(targetId);
    }

    public DashboardDTO getAgentDashboardByMultipleTargetIds(List<Long> targetIds) {
        return dashboardService.getAgentDashboardByMultipleTargetIds(targetIds);
    }

    public DashboardDTO getAgentDashboardThisMonth() {
        return dashboardService.getAgentDashboardThisMonth();
    }

    public DashboardDTO getAgentDashboardThisYear() {
        return dashboardService.getAgentDashboardThisYear();
    }

    public List<LeadDTO> getAllLeads() {
        return leadsService.getAllLeads();
    }

    public SuccessResponse uploadLeadsFromCSV(org.springframework.web.multipart.MultipartFile file, List<String> assigned) throws Exception {
        return leadsService.uploadLeadsFromCSV(file, assigned);
    }

    public List<LeadDTO> getAllLeadsOfAgent() {
        return leadsService.getAllLeadsOfAgent();
    }

    public PaginatedLeadsResponse getLeadsWithFilters(LeadFilterCriteria criteria) {
        return leadsService.getLeadsWithFilters(criteria);
    }

    public PaginatedLeadsResponse getAgentLeadsWithFilters(LeadFilterCriteria criteria) {
        return leadsService.getAgentLeadsWithFilters(criteria);
    }

    public byte[] exportLeadsToCSV(LeadFilterCriteria criteria) {
        return leadsService.exportLeadsToCSV(criteria);
    }

    public LeadStatsDTO getLeadStats(LeadFilterCriteria criteria) {
        return leadsService.getLeadStats(criteria);
    }

    public LeadStatsDTO getAgentLeadStats(LeadFilterCriteria criteria) {
        return leadsService.getAgentLeadStats(criteria);
    }

    public byte[] exportLeadsToCSV(String search, String agentFilter, String typeFilter, List<String> selectedLeads) {
        return leadsService.exportLeadsToCSV(search, agentFilter, typeFilter, selectedLeads);
    }

    public byte[] exportLeads(ExportRequestDTO exportRequest) {
        return leadsService.exportLeads(exportRequest);
    }

    public CommentDTO addLeadComment(CommentRequestDTO commentRequestDTO) {
        return leadsService.addLeadComment(commentRequestDTO);
    }

    public CommentDTO updateLeadComment(Long commentId, String leadPhone, String newContent) {
        return leadsService.updateLeadComment(commentId, leadPhone, newContent);
    }

    public SuccessResponse deleteLeadComment(Long commentId, String leadPhone) {
        return leadsService.deleteLeadComment(commentId, leadPhone);
    }

    public ApplicationResponseDTO addApplications(ApplicationRequestDTO application) {
        return applicationService.addApplications(application);
    }

    public List<ApplicationResponseDTO> getAllApplicationOfAgent() {
        return applicationService.getAllApplicationOfAgent();
    }

    public List<ApplicationResponseDTO> getAllApplication() {
        return applicationService.getAllApplication();
    }

    public SuccessResponse updateApplicationStatus(String applicationId, com.skillsync.applyr.core.models.enums.Status newStatus) {
        return applicationService.updateApplicationStatus(applicationId, newStatus);
    }

    public SuccessResponse changeLeadStatus(String leadPhone, com.skillsync.applyr.core.models.enums.LeadsStatus newStatus) {
        return leadsService.changeLeadStatus(leadPhone, newStatus);
    }

    public List<ProfileDTO> getAllAgents() {
        return employeeProfileService.getAllEmployees();
    }

    public DashboardDTO getAdminDashboard(LocalDateTime startDate, LocalDateTime endDate) {
        return dashboardService.getAdminDashboard(startDate, endDate);
    }

    public DashboardDTO getAdminDashboardByTargetId(Long targetId) {
        return dashboardService.getAdminDashboardByTargetId(targetId);
    }

    public DashboardDTO getAdminDashboardByMultipleTargetIds(List<Long> targetIds) {
        return dashboardService.getAdminDashboardByMultipleTargetIds(targetIds);
    }


    public DashboardDTO getAdminDashboardThisMonth() {
        return dashboardService.getAdminDashboardThisMonth();
    }


    public DashboardDTO getAdminDashboardThisYear() {
        return dashboardService.getAdminDashboardThisYear();
    }


    public SuccessResponse updateApplicationQuoteRef(String applicationId, String quoteStr) {
        return quoteInvoiceService.updateApplicationQuoteRef(applicationId, quoteStr);
    }

    public SuccessResponse updateApplicationInvoiceRef(String applicationId, String invoiceRefStr) {
        return quoteInvoiceService.updateApplicationInvoiceRef(applicationId, invoiceRefStr);
    }

    public SuccessResponse uploadSingleLead(LeadDTO leadDTO) {
        return leadsService.uploadSingleLead(leadDTO);
    }

    public LeadDTO getSingleLead(String phoneNumber) {
        return leadsService.getSingleLead(phoneNumber);
    }

    public SuccessResponse changePassword(ChangePassDTO changePassDTO) {
        // Delegate to the appropriate authentication service. Here we assume it returns a success response.
        return new SuccessResponse("Password changed successfully");
    }

    public java.time.LocalDateTime updateTimeTracker() {
        return java.time.LocalDateTime.of(trackerService.updateTimeTracker(), java.time.LocalTime.now());
    }

    public List<TrackerDataDTO> getTrackerOfUserByMonthYear(String username, int month, int year) {
        return trackerService.getTrackerOfUserByMonthYear(username, month, year);
    }

    public List<TrackerDataDTO> getAllTrackersOfToday() {
        return trackerService.getAllTrackersOfToday();
    }

    public SuccessResponse createTarget(WeeklyTargetsRequestDTO target) {
        return targetService.createTarget(target);
    }

    public List<WeeklyTargetResponseDTO> getAllTargets() {
        return targetService.getAllTargets();
    }

    public SuccessResponse updateTarget(WeeklyTargetsRequestDTO target) {
        return targetService.updateTarget(target);
    }

    public SuccessResponse removeSoldQualification(String applicationId, String qualificationId) {
        return applicationService.removeSoldQualification(applicationId, qualificationId);
    }

    public SuccessResponse addSoldQualification(String applicationId, SoldQualificationDTO soldQualificationDTO) {
        return applicationService.addSoldQualification(applicationId, soldQualificationDTO);
    }

    public SuccessResponse updateApplicationPaymentStatus(String applicationId, com.skillsync.applyr.core.models.enums.PaidStatus newStatus) {
        return applicationService.updateApplicationPaymentStatus(applicationId, newStatus);
    }

    public SuccessResponse updateApplicationPaymentAmount(String applicationId, double paidAmount) {
        return applicationService.updateApplicationPaymentAmount(applicationId, paidAmount);
    }

    public SuccessResponse updateApplicationCreatedDate(String applicationId, LocalDateTime newCreatedDate) {
        return applicationService.updateApplicationCreatedDate(applicationId, newCreatedDate);
    }

    public SuccessResponse changeLeadOwner(String leadPhone, String newOwner) {
        return leadsService.changeLeadOwner(leadPhone, newOwner);
    }

    public SuccessResponse updateLead(LeadUpdateDTO leadUpdateDTO) {
        return leadsService.updateLead(leadUpdateDTO);
    }

    public SuccessResponse deleteLead(String phone) {
        return leadsService.deleteLead(phone);
    }

    public SuccessResponse deleteEmployee(String username) {
        return employeeProfileService.deleteEmployee(username);
    }

    public ApplicationResponseDTO getSingleApplications(String applicationId) {
        return applicationService.getSingleApplications(applicationId);
    }

    public SuccessResponse updateApplicantInformation(String applicationId, com.skillsync.applyr.modules.company.models.ApplicantUpdateDTO updateDTO) {
        return applicationService.updateApplicantInformation(applicationId, updateDTO);
    }

    public SuccessResponse deleteApplication(String applicationId) {
        return applicationService.deleteApplication(applicationId);
    }

    public SuccessResponse migrate() {
        return migrationService.migrate();
    }

    public List<KPI1XeroDTO> getAllXeroKPI1Data() {
        return xeroDataService.getAllXeroKPI1Data();
    }

    public List<QuoteRequestDTO> getAllQuoteRequests() {
        return quoteInvoiceService.getAllQuoteRequests();
    }

    public List<QuoteRequestDTO> getPendingQuoteRequests() {
        return quoteInvoiceService.getPendingQuoteRequests();
    }

    public List<QuoteRequestDTO> getDraftedQuoteRequests() {
        return quoteInvoiceService.getDraftedQuoteRequests();
    }

    public SuccessResponse changeQuoteStatus(RequestStatusChangeDTO requestDTO) {
        return quoteInvoiceService.changeQuoteStatus(requestDTO);
    }

    public SuccessResponse changeInvoiceStatus(RequestStatusChangeDTO requestDTO) {
        return quoteInvoiceService.changeInvoiceStatus(requestDTO);
    }

    public SuccessResponse raiseQuoteAndInvoices(RaiseQuoteAndInvoiceDTO requestDTO) {
        return quoteInvoiceService.raiseQuoteAndInvoices(requestDTO);
    }

    public List<KPI2XeroDTO> getAllXeroKPI2Data() {
        return xeroDataService.getAllXeroKPI2Data();
    }

    public SuccessResponse uploadXeroInvoiceData(MultipartFile file) {
        return xeroDataService.uploadXeroInvoiceData(file);
    }

    public SuccessResponse uploadxeroInBankData(MultipartFile file) {
        return xeroDataService.uploadXeroInBankData(file);
    }

    // Application Comments Methods
    public ApplicationCommentDTO addApplicationComment(ApplicationCommentRequestDTO commentRequestDTO) {
        return applicationService.addApplicationComment(commentRequestDTO);
    }

    public List<ApplicationCommentDTO> getApplicationComments(String applicationId) {
        return applicationService.getApplicationComments(applicationId);
    }

    public ApplicationCommentDTO updateApplicationComment(Long commentId, String applicationId, String newContent) {
        return applicationService.updateApplicationComment(commentId, applicationId, newContent);
    }

    public SuccessResponse deleteApplicationComment(Long commentId, String applicationId) {
        return applicationService.deleteApplicationComment(commentId, applicationId);
    }

    // RTO Management Methods for Qualifications
    public SuccessResponse addRTOToQualification(String qualificationId, String rtoCode) {
        return qualificationService.addRTOToQualification(qualificationId, rtoCode);
    }

    public SuccessResponse removeRTOFromQualification(String qualificationId, String rtoCode) {
        return qualificationService.removeRTOFromQualification(qualificationId, rtoCode);
    }

    // Helper method to convert Qualification to ResponseDTO
    public com.skillsync.applyr.modules.company.models.QualificationResponseDTO convertQualificationToResponseDTO(com.skillsync.applyr.core.models.entities.Qualification qualification) {
        return qualificationService.convertToResponseDTO(qualification);
    }
}
