// src/components/AgentQualifications.js

import React, { useState, useMemo } from "react";
import { useGetQualificationsQuery } from "../../../services/SalesAPIService";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faEdit, faTrash, faBuilding, faTimes } from "@fortawesome/free-solid-svg-icons";
// Import the qualification mutations from the AdminAPIService
import { useAddQualificationMutation, useEditQualificationMutation, useDeleteQualificationMutation } from "../../../services/AdminAPIService";
import { useGetAllRTOsQuery, useFetchAndSaveRTOMutation } from "../../../services/CompanyAPIService";
import { useAddRTOToQualificationMutation, useRemoveRTOFromQualificationMutation } from "../../../services/AdminAPIService";
import EditQualificationModal from "../../../components/modal/EditQualificationModal";
import BulkImportModal from "../../../components/modal/BulkImportModal";
import ConfirmationDialog from "../../../components/common/ConfirmationDialog";
import { toast } from "react-toastify";

const Qualifications = () => {
  // State for filtering and sorting
  const [filter, setFilter] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");
  const [selectedType, setSelectedType] = useState("All"); // State for type filter

  // Drawer states
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedQualification, setSelectedQualification] = useState(null);

  // RTO-related states
  const [showAddRTOModal, setShowAddRTOModal] = useState(false);
  const [selectedRTOCode, setSelectedRTOCode] = useState("");
  const [newRTOCode, setNewRTOCode] = useState("");
  const [addMode, setAddMode] = useState("existing"); // "existing" or "new"

  // Modal states for Add Qualification
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newQualificationData, setNewQualificationData] = useState({
    qualificationId: "",
    qualificationName: "",
    rplPrice: 0,
    rtoPriceHigh: 0,
    enrollmentPrice: 0,
    offshorePrice: 0,
    notes: "",
    type: "",
    processingTime: "",
    demand: "",
    checklist: [],
  });

  // Bulk import modal state
  const [isBulkImportModalOpen, setIsBulkImportModalOpen] = useState(false);

  // Modal states for Edit Qualification
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [qualificationToEdit, setQualificationToEdit] = useState(null);

  // Delete confirmation dialog states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [qualificationToDelete, setQualificationToDelete] = useState(null);

  // Fetch qualifications using the SalesAPIService hook
  const {
    data: qualifications = [],
    error,
    isLoading,
    isFetching,
    refetch,
  } = useGetQualificationsQuery();

  // Hooks to call qualification mutations
  const [addQualification, { isLoading: isAddLoading }] = useAddQualificationMutation();
  const [editQualification, { isLoading: isEditLoading }] = useEditQualificationMutation();
  const [deleteQualification, { isLoading: isDeleteLoading }] = useDeleteQualificationMutation();

  // RTO-related hooks
  const { data: rtos = [], isLoading: isRTOsLoading } = useGetAllRTOsQuery();
  const [addRTOToQualification, { isLoading: isAddingRTO }] = useAddRTOToQualificationMutation();
  const [removeRTOFromQualification, { isLoading: isRemovingRTO }] = useRemoveRTOFromQualificationMutation();
  const [fetchAndSaveRTO, { isLoading: isFetchingRTO }] = useFetchAndSaveRTOMutation();

  // Extract unique types from qualifications data
  const uniqueTypes = useMemo(() => {
    const types = qualifications.map((q) => q.type);
    return ["All", ...Array.from(new Set(types))];
  }, [qualifications]);

  // Handle sort order toggle
  const handleSortChange = () => {
    setSortOrder((prevOrder) => (prevOrder === "asc" ? "desc" : "asc"));
  };

  // Handle Information button click
  const handleInfoClick = (qualification) => {
    setSelectedQualification(qualification);
    setIsDrawerOpen(true);
  };

  // Handle drawer close
  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
    setSelectedQualification(null);
  };

  // Filter and sort logic for qualifications
  const filteredQualifications = useMemo(() => {
    return qualifications
      .filter((qualification) => {
        // Filter by search input
        if (filter) {
          const lowerFilter = filter.toLowerCase();
          const matchesSearch =
            qualification.qualificationId.toLowerCase().includes(lowerFilter) ||
            qualification.qualificationName.toLowerCase().includes(lowerFilter);
          if (!matchesSearch) return false;
        }

        // Filter by type
        if (selectedType !== "All") {
          return qualification.type === selectedType;
        }

        return true;
      })
      .sort((a, b) => {
        if (sortOrder === "asc") {
          return a.qualificationName.localeCompare(b.qualificationName);
        } else {
          return b.qualificationName.localeCompare(a.qualificationName);
        }
      });
  }, [qualifications, filter, sortOrder, selectedType]);

  // Helper function to display price or N/A
  const displayPrice = (price) =>
    price === 0 ? "N/A" : `$${price.toFixed(2)}`;

  // ---------- Add Qualification Logic ----------
  const handleOpenAddModal = () => {
    // Reset form data when opening modal (optional)
    setNewQualificationData({
      qualificationId: "",
      qualificationName: "",
      rplPrice: 0,
      rtoPriceHigh: 0,
      enrollmentPrice: 0,
      offshorePrice: 0,
      notes: "",
      type: "",
      processingTime: "",
      demand: "",
      checklist: [],
    });
    setIsAddModalOpen(true);
  };

  const handleCloseAddModal = () => {
    setIsAddModalOpen(false);
  };

  const handleAddQualificationChange = (e) => {
    const { name, value } = e.target;
    setNewQualificationData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleAddQualificationSubmit = async (e) => {
    e.preventDefault();

    try {
      // Convert numeric fields from strings if necessary
      const payload = {
        ...newQualificationData,
        rplPrice: parseFloat(newQualificationData.rplPrice),
        enrollmentPrice: parseFloat(newQualificationData.enrollmentPrice),
        offshorePrice: parseFloat(newQualificationData.offshorePrice),
      };

      await addQualification(payload).unwrap();
      // After a successful add, refetch the qualifications list
      refetch();
      // Close the modal
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Error adding qualification:", error);
      // You might show an error toast or message here
    }
  };

  // Edit qualification handlers
  const handleOpenEditModal = (qualification) => {
    setQualificationToEdit(qualification);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setQualificationToEdit(null);
  };

  const handleEditSuccess = () => {
    // Refetch qualifications after successful edit
    refetch();
  };

  // Delete qualification handlers
  const handleOpenDeleteDialog = (qualification) => {
    setQualificationToDelete(qualification);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setQualificationToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (!qualificationToDelete) return;

    try {
      await deleteQualification(qualificationToDelete.qualificationId).unwrap();
      toast.success("Qualification deleted successfully!");
      refetch();
      handleCloseDeleteDialog();
    } catch (error) {
      toast.error(error?.data?.message || "Failed to delete qualification");
    }
  };

  // ---------- Bulk Import Logic ----------
  const handleOpenBulkImportModal = () => {
    setIsBulkImportModalOpen(true);
  };

  const handleCloseBulkImportModal = () => {
    setIsBulkImportModalOpen(false);
  };

  const handleBulkImportSuccess = () => {
    refetch(); // Refresh the qualifications list
    handleCloseBulkImportModal();
  };

  // ---------- RTO Management Logic ----------
  const handleAddRTO = () => {
    setShowAddRTOModal(true);
  };

  const handleCloseAddRTOModal = () => {
    setShowAddRTOModal(false);
    setSelectedRTOCode("");
    setNewRTOCode("");
    setAddMode("existing");
  };

  const handleAddRTOSubmit = async () => {
    if (!selectedQualification) return;

    try {
      let rtoCodeToAdd = "";

      if (addMode === "existing") {
        if (!selectedRTOCode) return;
        rtoCodeToAdd = selectedRTOCode;
      } else if (addMode === "new") {
        if (!newRTOCode.trim()) {
          toast.error("Please enter an RTO code");
          return;
        }

        // First, fetch and save the new RTO
        try {
          await fetchAndSaveRTO(newRTOCode.trim()).unwrap();
          toast.success("RTO data fetched and saved successfully!");
          rtoCodeToAdd = newRTOCode.trim();
        } catch (fetchError) {
          toast.error(fetchError?.data?.message || "Failed to fetch RTO information. Please check the code and try again.");
          return;
        }
      }

      // Then add the RTO to the qualification
      await addRTOToQualification({
        qualificationId: selectedQualification.qualificationId,
        rtoCode: rtoCodeToAdd
      }).unwrap();

      toast.success("RTO added to qualification successfully!");
      refetch(); // Refresh qualifications to get updated RTO list
      handleCloseAddRTOModal();
    } catch (error) {
      toast.error(error?.data?.message || "Failed to add RTO to qualification");
    }
  };

  const handleRemoveRTO = async (rtoCode) => {
    if (!selectedQualification) return;

    try {
      await removeRTOFromQualification({
        qualificationId: selectedQualification.qualificationId,
        rtoCode: rtoCode
      }).unwrap();

      toast.success("RTO removed from qualification successfully!");
      refetch(); // Refresh qualifications to get updated RTO list
    } catch (error) {
      toast.error(error?.data?.message || "Failed to remove RTO from qualification");
    }
  };

  const handleRTOClick = (rto) => {
    // Navigate to RTO profile page using RTO code
    window.open(`/admin/rto/${rto.code}`, '_blank');
  };

  return (
    <div className="w-full py-6">
      {/* Qualifications Section */}
      <div>
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Qualifications</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage qualification catalog and pricing
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-3">
            <button
              onClick={() => refetch()}
              className="flex items-center text-gray-700 bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
            <button
              onClick={handleOpenBulkImportModal}
              className="flex items-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Import Bulk
            </button>
            <button
              onClick={handleOpenAddModal}
              className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Qualification
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-50 p-4 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="w-full md:w-1/2 relative">
              <input
                type="text"
                placeholder="Search by ID or Name"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div className="flex items-center space-x-3 w-full md:w-auto">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="border border-gray-200 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                {uniqueTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
              <button
                onClick={handleSortChange}
                className="flex items-center bg-white border border-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                {sortOrder === "asc" ? "A-Z" : "Z-A"}
              </button>
            </div>
          </div>
        </div>

        {/* Handle Loading State */}
        {isLoading || isFetching ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
            <span className="ml-3 text-gray-500">Loading qualifications...</span>
          </div>
        ) : error ? (
          // Handle Error State
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Error fetching qualifications. Please try again later.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-x-auto">
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">Qualification List</h2>
            </div>
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-[#F4F5F9]">
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    RPL Low
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    RPL High
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Enrollment
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Offshore
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Processing Time
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Demand
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {filteredQualifications.length === 0 ? (
                  <tr>
                    <td
                      colSpan={10}
                      className="px-6 py-8 whitespace-nowrap text-sm text-gray-500 text-center"
                    >
                      <div className="flex flex-col items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p>{filter || selectedType !== "All" ? "No qualifications match your search." : "There are no available Qualifications."}</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredQualifications.map((qualification) => (
                    <tr key={qualification.id} className="hover:bg-[#F4F5F9] transition-colors">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-gray-900 bg-gray-100 px-2.5 py-0.5 rounded-full">
                          {qualification.qualificationId}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 max-w-xs truncate">
                        {qualification.qualificationName}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                          {qualification.type || "N/A"}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.rplPrice)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.rtoPriceHigh || 0)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.enrollmentPrice)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {displayPrice(qualification.offshorePrice)}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        {qualification.processingTime || "N/A"}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          qualification.demand === 'High' ? 'bg-red-100 text-red-800' :
                          qualification.demand === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          qualification.demand === 'Low' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {qualification.demand || "N/A"}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <button
                            onClick={() => handleInfoClick(qualification)}
                            className="text-[#6E39CB] hover:text-[#5E2CB8] transition-colors p-1"
                            title="View Details"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleOpenEditModal(qualification)}
                            className="text-blue-600 hover:text-blue-800 transition-colors p-1"
                            title="Edit"
                          >
                            <FontAwesomeIcon icon={faEdit} className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleOpenDeleteDialog(qualification)}
                            className="text-red-600 hover:text-red-800 transition-colors p-1"
                            title="Delete"
                          >
                            <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Right Drawer for Qualification Notes */}
      {isDrawerOpen && selectedQualification && (
        <div className="fixed inset-0 z-50 flex">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black opacity-50"
            onClick={handleDrawerClose}
          ></div>
          {/* Drawer */}
          <div className="relative ml-auto w-full max-w-md bg-white h-full shadow-xl overflow-y-auto">
            <div className="sticky top-0 bg-white z-10 border-b border-gray-100 p-6">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-xl font-bold text-gray-900">
                  Qualification Details
                </h2>
                <button
                  onClick={handleDrawerClose}
                  className="text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-gray-500">
                View detailed information about this qualification
              </p>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {selectedQualification.qualificationName}
                </h3>
                <div className="bg-[#F4F5F9] rounded-lg p-4 mb-6">
                  <div className="flex items-center">
                    <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Qualification ID</p>
                      <p className="text-base font-semibold text-gray-900">{selectedQualification.qualificationId}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Type</p>
                  <p className="text-base font-medium text-gray-900">{selectedQualification.type || "N/A"}</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Processing Time</p>
                  <p className="text-base font-medium text-gray-900">{selectedQualification.processingTime || "N/A"}</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Demand</p>
                  <p className="text-base font-medium text-gray-900">{selectedQualification.demand || "N/A"}</p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">RPL Low Price</p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.rplPrice)}
                  </p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">RPL High Price</p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.rtoPriceHigh || 0)}
                  </p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Enrollment Price
                  </p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.enrollmentPrice)}
                  </p>
                </div>

                <div className="bg-white rounded-lg border border-gray-100 p-4">
                  <p className="text-sm font-medium text-gray-500 mb-1">Offshore Price</p>
                  <p className="text-base font-medium text-gray-900">
                    {displayPrice(selectedQualification.offshorePrice)}
                  </p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm font-medium text-gray-500 mb-2">Notes</p>
                <div className="p-4 bg-[#F4F5F9] rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {selectedQualification.notes || "No notes available."}
                  </p>
                </div>
              </div>

              {/* RTO Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={faBuilding} className="h-5 w-5 text-[#6E39CB] mr-2" />
                    <h4 className="text-lg font-semibold text-gray-900">Associated RTOs</h4>
                  </div>
                  <button
                    onClick={handleAddRTO}
                    className="flex items-center bg-[#6E39CB] text-white px-3 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors text-sm"
                    disabled={isAddingRTO}
                  >
                    <FontAwesomeIcon icon={faPlus} className="h-4 w-4 mr-2" />
                    Add RTO
                  </button>
                </div>

                {selectedQualification.rtos && selectedQualification.rtos.length > 0 ? (
                  <div className="space-y-3">
                    {selectedQualification.rtos.map((rto) => (
                      <div
                        key={rto.id}
                        className="bg-white rounded-lg border border-gray-100 p-4 hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => handleRTOClick(rto)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <span className="text-sm font-medium text-gray-900 bg-gray-100 px-2.5 py-0.5 rounded-full mr-3">
                                {rto.code}
                              </span>
                              <h5 className="text-base font-semibold text-gray-900">{rto.legalName}</h5>
                            </div>
                            {rto.businessName && rto.businessName !== rto.legalName && (
                              <p className="text-sm text-gray-600 mb-1">Business: {rto.businessName}</p>
                            )}
                            {rto.address && (
                              <p className="text-sm text-gray-500">{rto.address}</p>
                            )}
                            {rto.rtoType && (
                              <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mt-2">
                                {rto.rtoType}
                              </span>
                            )}
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveRTO(rto.code);
                            }}
                            className="text-red-600 hover:text-red-800 transition-colors p-2 ml-3"
                            title="Remove RTO"
                            disabled={isRemovingRTO}
                          >
                            <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <FontAwesomeIcon icon={faBuilding} className="h-12 w-12 text-gray-300 mb-3" />
                    <p className="text-gray-500 text-sm">No RTOs associated with this qualification</p>
                    <p className="text-gray-400 text-xs mt-1">Click "Add RTO" to associate RTOs</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Qualification Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black opacity-50"
            onClick={handleCloseAddModal}
          ></div>
          {/* Modal content */}
          <div className="bg-white rounded-lg shadow-xl z-10 p-6 w-full max-w-xl relative">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">Add New Qualification</h2>
              <button
                onClick={handleCloseAddModal}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleAddQualificationSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="qualificationId"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Qualification ID
                </label>
                <input
                  type="text"
                  id="qualificationId"
                  name="qualificationId"
                  value={newQualificationData.qualificationId}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="qualificationName"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Qualification Name
                </label>
                <input
                  type="text"
                  id="qualificationName"
                  name="qualificationName"
                  value={newQualificationData.qualificationName}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  required
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  Type
                </label>
                <input
                  type="text"
                  id="type"
                  name="type"
                  value={newQualificationData.type}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  required
                />
              </div>

              <div>
                <label htmlFor="rplPrice" className="block text-sm font-medium text-gray-700 mb-2">
                  RPL Low Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  id="rplPrice"
                  name="rplPrice"
                  value={newQualificationData.rplPrice}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>

              <div>
                <label htmlFor="rtoPriceHigh" className="block text-sm font-medium text-gray-700 mb-2">
                  RPL High Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  id="rtoPriceHigh"
                  name="rtoPriceHigh"
                  value={newQualificationData.rtoPriceHigh}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>

              <div>
                <label
                  htmlFor="enrollmentPrice"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Enrollment Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  id="enrollmentPrice"
                  name="enrollmentPrice"
                  value={newQualificationData.enrollmentPrice}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>

              <div>
                <label
                  htmlFor="offshorePrice"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Offshore Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  id="offshorePrice"
                  name="offshorePrice"
                  value={newQualificationData.offshorePrice}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>

              <div>
                <label htmlFor="processingTime" className="block text-sm font-medium text-gray-700 mb-2">
                  Processing Time
                </label>
                <input
                  type="text"
                  id="processingTime"
                  name="processingTime"
                  value={newQualificationData.processingTime}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  placeholder="e.g., 6-8 weeks"
                />
              </div>

              <div>
                <label htmlFor="demand" className="block text-sm font-medium text-gray-700 mb-2">
                  Demand
                </label>
                <input
                  type="text"
                  id="demand"
                  name="demand"
                  value={newQualificationData.demand}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  placeholder="e.g., High, Medium, Low"
                />
              </div>

              <div className="col-span-1 md:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={newQualificationData.notes}
                  onChange={handleAddQualificationChange}
                  className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  rows="3"
                />
              </div>

              <div className="col-span-1 md:col-span-2 flex justify-end gap-3 mt-4">
                <button
                  type="button"
                  onClick={handleCloseAddModal}
                  className="border border-gray-300 text-gray-700 px-5 py-2.5 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isAddLoading}
                  className="bg-[#6E39CB] text-white px-5 py-2.5 rounded-lg hover:bg-[#5E2CB8] transition-colors flex items-center"
                >
                  {isAddLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    "Save Qualification"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Qualification Modal */}
      <EditQualificationModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        qualification={qualificationToEdit}
        onSuccess={handleEditSuccess}
      />

      {/* Bulk Import Modal */}
      <BulkImportModal
        isOpen={isBulkImportModalOpen}
        onClose={handleCloseBulkImportModal}
        onSuccess={handleBulkImportSuccess}
      />

      {/* Add RTO Modal */}
      {showAddRTOModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black opacity-50"
            onClick={handleCloseAddRTOModal}
          ></div>
          {/* Modal content */}
          <div className="bg-white rounded-lg shadow-xl z-10 p-6 w-full max-w-md relative">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">Add RTO to Qualification</h2>
              <button
                onClick={handleCloseAddRTOModal}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <FontAwesomeIcon icon={faTimes} className="h-6 w-6" />
              </button>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-4">
                Add an RTO to associate with <strong>{selectedQualification?.qualificationName}</strong>
              </p>

              {/* Mode Selection */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Choose Option
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="addMode"
                      value="existing"
                      checked={addMode === "existing"}
                      onChange={(e) => setAddMode(e.target.value)}
                      className="mr-2 text-[#6E39CB] focus:ring-[#6E39CB]"
                    />
                    <span className="text-sm text-gray-700">Select Existing RTO</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="addMode"
                      value="new"
                      checked={addMode === "new"}
                      onChange={(e) => setAddMode(e.target.value)}
                      className="mr-2 text-[#6E39CB] focus:ring-[#6E39CB]"
                    />
                    <span className="text-sm text-gray-700">Fetch New RTO</span>
                  </label>
                </div>
              </div>

              {/* Existing RTO Selection */}
              {addMode === "existing" && (
                <div>
                  <label htmlFor="rtoSelect" className="block text-sm font-medium text-gray-700 mb-2">
                    Select RTO
                  </label>
                  {isRTOsLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#6E39CB]"></div>
                      <span className="ml-2 text-gray-500">Loading RTOs...</span>
                    </div>
                  ) : (
                    <select
                      id="rtoSelect"
                      value={selectedRTOCode}
                      onChange={(e) => setSelectedRTOCode(e.target.value)}
                      className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    >
                      <option value="">Select an RTO...</option>
                      {rtos
                        .filter(rto => !selectedQualification?.rtos?.some(qRto => qRto.code === rto.code))
                        .map((rto) => (
                          <option key={rto.id} value={rto.code}>
                            {rto.code} - {rto.legalName}
                          </option>
                        ))}
                    </select>
                  )}
                </div>
              )}

              {/* New RTO Code Input */}
              {addMode === "new" && (
                <div>
                  <label htmlFor="newRTOCode" className="block text-sm font-medium text-gray-700 mb-2">
                    RTO Code
                  </label>
                  <input
                    type="text"
                    id="newRTOCode"
                    value={newRTOCode}
                    onChange={(e) => setNewRTOCode(e.target.value)}
                    placeholder="Enter RTO code (e.g., 12345)"
                    className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This will fetch RTO information from training.gov.au and save it to the system.
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={handleCloseAddRTOModal}
                className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddRTOSubmit}
                disabled={
                  (addMode === "existing" && !selectedRTOCode) ||
                  (addMode === "new" && !newRTOCode.trim()) ||
                  isAddingRTO ||
                  isFetchingRTO
                }
                className="bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {(isAddingRTO || isFetchingRTO) ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    {isFetchingRTO ? "Fetching RTO..." : "Adding..."}
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faPlus} className="h-4 w-4 mr-2" />
                    {addMode === "new" ? "Fetch & Add RTO" : "Add RTO"}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Qualification"
        message={`Are you sure you want to delete "${qualificationToDelete?.qualificationName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={isDeleteLoading}
      />
    </div>
  );
};

export default Qualifications;
