import React from "react";
import { FaCommentDots, FaExternalLinkAlt, FaEdit, FaTrash } from "react-icons/fa";
import { getStatusClass, formatDate } from "../../utils/leadUtils";

const LeadTableRow = ({
  lead,
  onProfileRedirect,
  onStatusChange,
  onEditLead,
  onOpenDrawer,
  onDeleteLead,
  isAdmin = false,
  isUpdatingStatus = false
}) => {
  return (
    <tr className="hover:bg-[#F4F5F9] transition-colors">
      <td className="px-3 py-3">
        <div className="text-sm font-medium text-gray-900 truncate max-w-[200px]" title={lead.companyName}>
          {lead.companyName && lead.companyName.toLowerCase() !== "n/a" ? (
            <span className="inline-flex items-center w-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              <span className="truncate">{lead.companyName}</span>
            </span>
          ) : (
            <span className="text-gray-500 italic">Individual Lead</span>
          )}
        </div>
      </td>
      <td className="px-3 py-3">
        <div className="text-sm font-medium text-gray-900 truncate max-w-[150px]" title={lead.leadName}>
          {lead.leadName}
        </div>
      </td>
      <td className="px-3 py-3">
        <div className="text-sm text-gray-700 truncate max-w-[120px]" title={lead.phone}>
          {lead.phone}
        </div>
      </td>
      <td className="px-3 py-3">
        <div className="text-sm truncate max-w-[200px]" title={lead.email}>
          {lead.email ? (
            <a
              href={`mailto:${lead.email}`}
              className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              {lead.email}
            </a>
          ) : (
            <span className="text-gray-400">No email</span>
          )}
        </div>
      </td>
      <td className="px-3 py-3">
        <div className="text-sm text-gray-700 truncate max-w-[120px]" title={formatDate(lead.createdDate)}>
          {formatDate(lead.createdDate)}
        </div>
      </td>
      {/* Conditional column rendering based on user role */}
      {isAdmin ? (
        <td className="px-3 py-3">
          <div className="text-sm text-gray-700 truncate max-w-[200px]">
            {lead.assignedAgents && lead.assignedAgents.length > 0 ? (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#6E39CB] mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span className="truncate" title={lead.assignedAgents.map(agent => agent.fullName).join(', ')}>
                  {lead.assignedAgents.length === 1
                    ? lead.assignedAgents[0].fullName
                    : `${lead.assignedAgents[0].fullName} +${lead.assignedAgents.length - 1} more`
                  }
                </span>
              </div>
            ) : (
              <span className="text-gray-400 italic flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Unassigned
              </span>
            )}
          </div>
        </td>
      ) : (
        <td className="px-3 py-3">
          <div className="text-sm text-gray-700 truncate max-w-[200px]">
            {lead.comments && lead.comments.length > 0 ? (
              (() => {
                // Sort comments by date and get the latest one
                const sortedComments = [...lead.comments].sort((a, b) =>
                  new Date(b.createdAt || b.commentedAt) - new Date(a.createdAt || a.commentedAt)
                );
                const latestComment = sortedComments[0];
                const commentText = latestComment.content || latestComment.comment || '';

                return (
                  <div title={commentText} className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#6E39CB] mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span className="truncate">
                      {commentText.length > 50 ? `${commentText.substring(0, 50)}...` : commentText}
                    </span>
                  </div>
                );
              })()
            ) : (
              <span className="text-gray-400 italic flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                No comments
              </span>
            )}
          </div>
        </td>
      )}

      <td className="px-3 py-3 whitespace-nowrap">
        <select
          className={`rounded-full px-3 py-1 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-[#6E39CB] ${getStatusClass(lead.status)}`}
          value={lead.status}
          onChange={(e) => onStatusChange(lead.phone, e.target.value)}
          disabled={isUpdatingStatus}
        >
          <option value="HOT">HOT</option>
          <option value="WARM">WARM</option>
          <option value="COLD">COLD</option>
          <option value="FRESH">FRESH</option>
          <option value="CLOSED">CLOSED</option>
        </select>
        {isUpdatingStatus && (
          <span className="ml-2 text-[#6E39CB] text-xs flex items-center mt-1">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Updating...
          </span>
        )}
      </td>
      <td className="px-3 py-3 whitespace-nowrap">
        <div className="text-sm text-gray-700">
          {lead.applicationCount > 0 ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-green-100 text-green-800">
              {lead.applicationCount}
            </span>
          ) : (
            <span className="text-gray-500">0</span>
          )}
        </div>
      </td>

      <td className="px-3 py-3 whitespace-nowrap text-center">
        <div className="flex justify-center space-x-1">
          <button
            onClick={() => onProfileRedirect(lead.phone)}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
            title="Open Profile"
          >
            <FaExternalLinkAlt size={12} />
          </button>
          <button
            onClick={() => {
              onOpenDrawer(lead);
            }}
            className={`p-1 rounded-full hover:bg-[#F4F5F9] ${
              lead.comments && lead.comments.length > 0
                ? "text-[#6E39CB] hover:text-[#5E2CB8]"
                : "text-gray-400 hover:text-[#6E39CB]"
            }`}
            title={`${lead.comments && lead.comments.length > 0 ? 'View' : 'Add'} Comments`}
          >
            <FaCommentDots size={12} />
          </button>
          <button
            onClick={() => onEditLead(lead)}
            className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
            title="Edit Lead"
          >
            <FaEdit size={12} />
          </button>
          {isAdmin && onDeleteLead && (
            <button
              onClick={() => onDeleteLead(lead)}
              className="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-50"
              title="Delete Lead"
            >
              <FaTrash size={12} />
            </button>
          )}
        </div>
      </td>
    </tr>
  );
};

export default LeadTableRow;
