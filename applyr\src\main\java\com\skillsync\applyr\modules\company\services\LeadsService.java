package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.Lead;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.core.models.enums.LeadsStatus;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.core.utills.UserUtils;

import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.company.repositories.CommentRepository;
import com.skillsync.applyr.modules.company.repositories.LeadsRepository;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.sales.models.ApplicationDTO;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LeadsService {

    private final LeadsRepository leadsRepository;
    private final SalesAgentRepository salesAgentRepository;
    private final ApplicationRepository applicationRepository;
    private final CommentRepository commentRepository;

    public LeadsService(LeadsRepository leadsRepository,
                        SalesAgentRepository salesAgentRepository,
                        ApplicationRepository applicationRepository,
                        CommentRepository commentRepository) {
        this.leadsRepository = leadsRepository;
        this.salesAgentRepository = salesAgentRepository;
        this.applicationRepository = applicationRepository;
        this.commentRepository = commentRepository;
    }

    public List<LeadDTO> getAllLeads() {
        List<com.skillsync.applyr.core.models.entities.Lead> allLeadsRaw = leadsRepository.findAll();
        return fromLeadsToLeadDTOs(allLeadsRaw);
    }

    public PaginatedLeadsResponse getLeadsWithFilters(LeadFilterCriteria criteria) {
        // Build sort
        Sort sort = Sort.unsorted();
        if (criteria.getSortField() != null && !criteria.getSortField().isEmpty()) {
            Sort.Direction direction = "desc".equalsIgnoreCase(criteria.getSortDirection())
                ? Sort.Direction.DESC
                : Sort.Direction.ASC;
            sort = Sort.by(direction, criteria.getSortField());
        } else {
            // Default sort by creation date (newest first)
            sort = Sort.by(Sort.Direction.DESC, "createdDate");
        }

        // Build pageable
        Pageable pageable = PageRequest.of(criteria.getPage(), criteria.getSize(), sort);

        // Get all leads first, then filter in memory for complex criteria
        // For simple cases, we can use repository methods
        Page<Lead> leadPage;

        // Parse date filters
        java.time.LocalDateTime startDate = null;
        java.time.LocalDateTime endDate = null;

        if (criteria.getDateFilterType() != null && !criteria.getDateFilterType().equals("all")) {
            java.time.LocalDateTime now = java.time.LocalDateTime.now();

            switch (criteria.getDateFilterType()) {
                case "today":
                    startDate = now.toLocalDate().atStartOfDay();
                    endDate = now.toLocalDate().atTime(23, 59, 59);
                    break;
                case "thisWeek":
                    java.time.DayOfWeek dayOfWeek = now.getDayOfWeek();
                    int daysFromMonday = dayOfWeek.getValue() - 1;
                    startDate = now.minusDays(daysFromMonday).toLocalDate().atStartOfDay();
                    endDate = startDate.plusDays(6).toLocalDate().atTime(23, 59, 59);
                    break;
                case "thisMonth":
                    startDate = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
                    endDate = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).toLocalDate().atTime(23, 59, 59);
                    break;
                case "thisYear":
                    startDate = now.withDayOfYear(1).toLocalDate().atStartOfDay();
                    endDate = now.withDayOfYear(now.toLocalDate().lengthOfYear()).toLocalDate().atTime(23, 59, 59);
                    break;
                case "custom":
                    startDate = criteria.getStartDate();
                    endDate = criteria.getEndDate();
                    break;
                case "specificDate":
                    if (criteria.getSpecificDate() != null) {
                        startDate = criteria.getSpecificDate().toLocalDate().atStartOfDay();
                        endDate = criteria.getSpecificDate().toLocalDate().atTime(23, 59, 59);
                    }
                    break;
            }
        }

        // Convert status filter to enum
        LeadsStatus statusEnum = null;
        final boolean isColdFreshFilter;
        if (criteria.getStatusFilter() != null && !criteria.getStatusFilter().equals("all") && !criteria.getStatusFilter().isEmpty()) {
            if (criteria.getStatusFilter().equals("COLD_FRESH")) {
                isColdFreshFilter = true;
            } else {
                isColdFreshFilter = false;
                try {
                    statusEnum = LeadsStatus.valueOf(criteria.getStatusFilter().toUpperCase());
                } catch (IllegalArgumentException e) {
                    // Invalid status, ignore
                }
            }
        } else {
            isColdFreshFilter = false;
        }

        // Use appropriate repository method based on filters
        if (criteria.getSearch() != null && !criteria.getSearch().isEmpty()) {
            // Use search query
            leadPage = leadsRepository.findBySearchTerm(criteria.getSearch(), pageable);
        } else if (statusEnum != null && startDate != null && endDate != null) {
            // Status and date filter
            leadPage = leadsRepository.findByStatusAndCreatedDateBetween(statusEnum, startDate, endDate, pageable);
        } else if (statusEnum != null) {
            // Status filter only
            leadPage = leadsRepository.findByStatus(statusEnum, pageable);
        } else if (startDate != null && endDate != null) {
            // Date filter only
            leadPage = leadsRepository.findByCreatedDateBetween(startDate, endDate, pageable);
        } else {
            // No filters, get all
            leadPage = leadsRepository.findAll(pageable);
        }

        // Apply additional filters in memory (agent, application count)
        List<Lead> filteredLeads = leadPage.getContent().stream()
            .filter(lead -> {
                // Agent filter
                if (criteria.getAgentFilter() != null && !criteria.getAgentFilter().isEmpty()) {
                    boolean hasAgent = lead.getAssignedAgents().stream()
                        .anyMatch(agent -> agent.getFullName().equals(criteria.getAgentFilter()));
                    if (!hasAgent) return false;
                }

                // Application filter - calculate actual application count from database
                if (criteria.getApplicationFilter() != null && !criteria.getApplicationFilter().equals("all")) {
                    List<Application> applications = applicationRepository.findAllByLeadPhone(lead.getPhone());
                    int actualApplicationCount = applications.size();

                    if (criteria.getApplicationFilter().equals("hasApplications") && actualApplicationCount == 0) {
                        return false;
                    }
                    if (criteria.getApplicationFilter().equals("noApplications") && actualApplicationCount > 0) {
                        return false;
                    }
                }

                // Lead type filter (B2B vs Direct)
                if (criteria.getLeadTypeFilter() != null && !criteria.getLeadTypeFilter().equals("all")) {
                    if (criteria.getLeadTypeFilter().equals("B2B")) {
                        // B2B: company name exists and is not "n/a"
                        if (lead.getCompanyName() == null || lead.getCompanyName().toLowerCase().equals("n/a")) {
                            return false;
                        }
                    } else if (criteria.getLeadTypeFilter().equals("Direct")) {
                        // Direct: company name is null or "n/a"
                        if (lead.getCompanyName() != null && !lead.getCompanyName().toLowerCase().equals("n/a")) {
                            return false;
                        }
                    }
                }

                // Handle COLD_FRESH special filter
                if (isColdFreshFilter) {
                    if (lead.getStatus() != LeadsStatus.COLD && lead.getStatus() != LeadsStatus.FRESH) {
                        return false;
                    }
                }

                return true;
            })
            .collect(java.util.stream.Collectors.toList());

        // Convert to DTOs
        List<LeadDTO> leadDTOs = filteredLeads.stream()
            .map(this::fromSingleLeadToSingleLeadDTO)
            .collect(java.util.stream.Collectors.toList());

        // Build response (note: total counts might be approximate due to in-memory filtering)
        PaginatedLeadsResponse response = new PaginatedLeadsResponse();
        response.setLeads(leadDTOs);
        response.setCurrentPage(leadPage.getNumber());
        response.setPageSize(leadPage.getSize());
        response.setTotalElements(leadPage.getTotalElements()); // This is approximate
        response.setTotalPages(leadPage.getTotalPages());
        response.setHasNext(leadPage.hasNext());
        response.setHasPrevious(leadPage.hasPrevious());

        return response;
    }

    public SuccessResponse uploadLeadsFromCSV(MultipartFile file, List<String> assigned) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("Uploaded file is empty");
        }

        List<com.skillsync.applyr.core.models.entities.Lead> leads = new ArrayList<>();

        try (
                Reader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));
                CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT
                        .builder()
                        .setHeader()
                        .setSkipHeaderRecord(true)
                        .setTrim(true)
                        .build());
        ) {
            Iterable<CSVRecord> csvRecords = csvParser.getRecords();
            List<SalesAgent> salesAgents = new ArrayList<>();
            if (assigned.isEmpty()) {
                salesAgents.add(salesAgentRepository
                        .getSalesAgentByUserUsername(UserUtils.getUsernameFromToken()).get());
            } else {
                for (String s : assigned) {
                    salesAgents.add(salesAgentRepository.getSalesAgentByUserUsername(s).get());
                }
            }

            for (CSVRecord csvRecord : csvRecords) {
                com.skillsync.applyr.core.models.entities.Lead lead = new com.skillsync.applyr.core.models.entities.Lead();

                lead.setCompanyName(csvRecord.get("company_name"));
                lead.setLeadName(csvRecord.get("lead_name"));
                lead.setPhone(csvRecord.get("phone"));
                lead.setEmail(csvRecord.get("email"));
                lead.setAddress(csvRecord.get("address"));

                lead.setStatus(LeadsStatus.FRESH);
                lead.setApplicationCount(0);
                for (SalesAgent salesAgent : salesAgents) {
                    lead.addSalesAgent(salesAgent);
                }
                List<com.skillsync.applyr.core.models.entities.Lead> raw = leadsRepository.findAllLeadByPhoneOrEmail(lead.getPhone(), lead.getEmail());
                if (!raw.isEmpty()) {
                    continue;
                }
                leads.add(lead);
            }

            leadsRepository.saveAll(leads);
            return new SuccessResponse("Leads added successfully");
        } catch (Exception e) {
            throw new AppRTException("Failed to parse and upload CSV file" + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public List<LeadDTO> getAllLeadsOfAgent() {
        String username = UserUtils.getUsernameFromToken();
        Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(username);
        if (salesAgent.isPresent()) {
            return fromLeadsToLeadDTOs(new ArrayList<>(salesAgent.get().getLeads()));
        }
        throw new AppRTException("Unable to fetch leads information", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public PaginatedLeadsResponse getAgentLeadsWithFilters(LeadFilterCriteria criteria) {
        String username = UserUtils.getUsernameFromToken();
        Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(username);

        if (salesAgent.isEmpty()) {
            throw new AppRTException("Unable to fetch agent information", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // Set the agent filter to current agent to restrict results
        criteria.setAgentFilter(salesAgent.get().getFullName());

        // Use the existing filtering method
        return getLeadsWithFilters(criteria);
    }

    public CommentDTO addLeadComment(CommentRequestDTO commentRequestDTO) {
        Optional<com.skillsync.applyr.core.models.entities.Lead> lead = leadsRepository.getLeadByPhone(commentRequestDTO.getUniqueId());
        if (lead.isPresent()) {
            com.skillsync.applyr.core.models.entities.Comment comment = new com.skillsync.applyr.core.models.entities.Comment();
            comment.setLead(lead.get());
            comment.setContent(commentRequestDTO.getComment());
            com.skillsync.applyr.core.models.entities.Activity activity = new com.skillsync.applyr.core.models.entities.Activity();
            activity.setLead(lead.get());
            activity.setDescription("User with username " +
                    UserUtils.getUsernameFromToken() + " has added a comment: " + commentRequestDTO.getComment());

            lead.get().getActivities().add(activity);
            lead.get().getComments().add(comment);
            leadsRepository.save(lead.get());

            return new CommentDTO(comment);
        }
        throw new AppRTException("Unable to add lead comment", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public CommentDTO updateLeadComment(Long commentId, String leadPhone, String newContent) {
        Optional<com.skillsync.applyr.core.models.entities.Comment> comment = commentRepository.findByIdAndLeadPhone(commentId, leadPhone);
        if (comment.isPresent()) {
            comment.get().setContent(newContent);
            com.skillsync.applyr.core.models.entities.Comment updatedComment = commentRepository.save(comment.get());
            return new CommentDTO(updatedComment);
        }
        throw new AppRTException("Unable to update lead comment", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse deleteLeadComment(Long commentId, String leadPhone) {
        Optional<com.skillsync.applyr.core.models.entities.Comment> comment = commentRepository.findByIdAndLeadPhone(commentId, leadPhone);
        if (comment.isPresent()) {
            commentRepository.delete(comment.get());
            return new SuccessResponse("Comment deleted successfully");
        }
        throw new AppRTException("Unable to delete lead comment", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse uploadSingleLead(LeadDTO leadDTO) {
        Optional<com.skillsync.applyr.core.models.entities.Lead> leadCheck = leadsRepository.findByEmail(leadDTO.getEmail());
        if (leadCheck.isPresent()) {
            throw new AppRTException("Lead already exists, Please contact Lead owner " +
                    leadCheck.get().getAssignedAgents().iterator().next().getFullName() +
                    " - Email: " + leadCheck.get().getAssignedAgents().iterator().next().getEmailAddress(), HttpStatus.CONFLICT);
        } else {
            Optional<com.skillsync.applyr.core.models.entities.Lead> leadCheck2 = leadsRepository.getLeadByPhone(leadDTO.getPhone());
            if (leadCheck2.isPresent()) {
                throw new AppRTException("Lead already exists, Please contact Lead owner " +
                        leadCheck2.get().getAssignedAgents().iterator().next().getFullName() +
                        " - Email: " + leadCheck2.get().getAssignedAgents().iterator().next().getEmailAddress(), HttpStatus.CONFLICT);
            }
        }
        com.skillsync.applyr.core.models.entities.Lead lead = new com.skillsync.applyr.core.models.entities.Lead(leadDTO);
        lead = leadsRepository.save(lead);
        for (SalesAgentDTO salesAgentDTO : leadDTO.getAssignedAgents()) {
            Optional<SalesAgent> salesAgent = salesAgentRepository
                    .getSalesAgentByUserUsername(salesAgentDTO.getUsername());
            salesAgent.ifPresent(lead::addSalesAgent);
        }
        leadsRepository.save(lead);
        return new SuccessResponse("Lead has been uploaded successfully");
    }

    public LeadDTO getSingleLead(String phoneNumber) {
        Optional<com.skillsync.applyr.core.models.entities.Lead> lead = leadsRepository.getLeadByPhone(phoneNumber);
        if (lead.isPresent()) {
            return fromSingleLeadToSingleLeadDTO(lead.get());
        }
        throw new AppRTException("Lead not found", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse changeLeadStatus(String leadPhone, LeadsStatus newStatus) {
        Optional<com.skillsync.applyr.core.models.entities.Lead> lead = leadsRepository.getLeadByPhone(leadPhone);
        if (lead.isEmpty()) {
            throw new AppRTException("Unable to find lead", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        lead.get().setStatus(newStatus);
        leadsRepository.save(lead.get());
        return new SuccessResponse("Lead updated successfully");
    }

    public SuccessResponse changeLeadOwner(String leadPhone, String newOwner) {
        Optional<com.skillsync.applyr.core.models.entities.Lead> lead = leadsRepository.getLeadByPhone(leadPhone);
        if (lead.isPresent()) {
            Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(newOwner);
            if (salesAgent.isPresent()) {
                lead.get().addSalesAgent(salesAgent.get());
                leadsRepository.save(lead.get());
                return new SuccessResponse("Lead updated successfully");
            } else {
                throw new AppRTException("Unable to find lead", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
        throw new AppRTException("Unable to change lead owner", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public SuccessResponse updateLead(LeadUpdateDTO leadUpdateDTO) {
        try {
            Optional<com.skillsync.applyr.core.models.entities.Lead> leadOptional = leadsRepository.getLeadByPhone(leadUpdateDTO.getPhone());
            if (leadOptional.isEmpty()) {
                throw new AppRTException("Lead not found with phone: " + leadUpdateDTO.getPhone(), HttpStatus.NOT_FOUND);
            }

            com.skillsync.applyr.core.models.entities.Lead lead = leadOptional.get();

            // Update lead properties
            lead.setCompanyName(leadUpdateDTO.getCompanyName());
            lead.setLeadName(leadUpdateDTO.getLeadName());
            lead.setEmail(leadUpdateDTO.getEmail());
            lead.setAddress(leadUpdateDTO.getAddress());

            leadsRepository.save(lead);
            return new SuccessResponse("Lead updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Unable to update lead: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse deleteLead(String phone) {
        try {
            Optional<com.skillsync.applyr.core.models.entities.Lead> leadOptional = leadsRepository.getLeadByPhone(phone);
            if (leadOptional.isEmpty()) {
                throw new AppRTException("Lead not found with phone: " + phone, HttpStatus.NOT_FOUND);
            }

            com.skillsync.applyr.core.models.entities.Lead lead = leadOptional.get();

            // Check if lead has any applications
            List<Application> applications = applicationRepository.findAllByLeadPhone(phone);
            if (!applications.isEmpty()) {
                throw new AppRTException("Cannot delete lead with existing applications. Please delete applications first.", HttpStatus.CONFLICT);
            }

            leadsRepository.delete(lead);
            return new SuccessResponse("Lead deleted successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Unable to delete lead: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public byte[] exportLeadsToCSV(LeadFilterCriteria criteria) {
        try {
            List<LeadDTO> leadsToExport;

            // If specific leads are selected, export only those
            if (criteria.getSelectedLeads() != null && !criteria.getSelectedLeads().isEmpty()) {
                List<Lead> selectedLeadEntities = criteria.getSelectedLeads().stream()
                    .map(phone -> leadsRepository.getLeadByPhone(phone))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
                leadsToExport = selectedLeadEntities.stream()
                    .map(this::fromSingleLeadToSingleLeadDTO)
                    .collect(Collectors.toList());
            } else {
                // Use filter criteria to get leads from database
                // Set a large page size for export (or implement streaming)
                criteria.setPage(0);
                criteria.setSize(Integer.MAX_VALUE);
                PaginatedLeadsResponse response = getLeadsWithFilters(criteria);
                leadsToExport = response.getLeads();
            }

            // Create CSV content
            StringWriter stringWriter = new StringWriter();
            CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                    .setHeader("Company Name", "Lead Name", "Phone", "Email", "Address", "Status", "Application Count", "Created Date", "Assigned Agents")
                    .build();

            try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy HH:mm");

                for (LeadDTO lead : leadsToExport) {
                    String assignedAgents = lead.getAssignedAgents().stream()
                            .map(SalesAgentDTO::getFullName)
                            .collect(Collectors.joining(", "));

                    String createdDate = lead.getCreatedDate() != null ?
                            lead.getCreatedDate().format(formatter) : "";

                    csvPrinter.printRecord(
                            lead.getCompanyName() != null ? lead.getCompanyName() : "",
                            lead.getLeadName() != null ? lead.getLeadName() : "",
                            lead.getPhone() != null ? lead.getPhone() : "",
                            lead.getEmail() != null ? lead.getEmail() : "",
                            lead.getAddress() != null ? lead.getAddress() : "",
                            lead.getStatus() != null ? lead.getStatus().toString() : "",
                            lead.getApplicationCount(),
                            createdDate,
                            assignedAgents
                    );
                }
            }

            return stringWriter.toString().getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new AppRTException("Failed to generate CSV export: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Backward compatibility method
    public byte[] exportLeadsToCSV(String search, String agentFilter, String typeFilter, List<String> selectedLeads) {
        LeadFilterCriteria criteria = new LeadFilterCriteria();
        criteria.setSearch(search);
        criteria.setAgentFilter(agentFilter);
        criteria.setStatusFilter(typeFilter);
        criteria.setSelectedLeads(selectedLeads);
        return exportLeadsToCSV(criteria);
    }

    // New enhanced export method with format and column selection
    public byte[] exportLeads(ExportRequestDTO exportRequest) {
        if ("EXCEL".equalsIgnoreCase(exportRequest.getFormat())) {
            return exportLeadsToExcel(exportRequest);
        } else {
            return exportLeadsToCSVWithColumns(exportRequest);
        }
    }

    private byte[] exportLeadsToCSVWithColumns(ExportRequestDTO exportRequest) {
        try {
            List<LeadDTO> leadsToExport = getLeadsForExport(exportRequest.toLeadFilterCriteria());
            List<ExportColumn> columnsToExport = getColumnsToExport(exportRequest.getSelectedColumns());

            StringWriter stringWriter = new StringWriter();

            // Build header from selected columns
            String[] headers = columnsToExport.stream()
                    .map(ExportColumn::getDisplayName)
                    .toArray(String[]::new);

            CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                    .setHeader(headers)
                    .build();

            try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy HH:mm");

                for (LeadDTO lead : leadsToExport) {
                    Object[] values = columnsToExport.stream()
                            .map(column -> getColumnValue(lead, column, formatter))
                            .toArray();
                    csvPrinter.printRecord(values);
                }
            }

            return stringWriter.toString().getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new AppRTException("Failed to generate CSV export: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private byte[] exportLeadsToExcel(ExportRequestDTO exportRequest) {
        try {
            List<LeadDTO> leadsToExport = getLeadsForExport(exportRequest.toLeadFilterCriteria());
            List<ExportColumn> columnsToExport = getColumnsToExport(exportRequest.getSelectedColumns());

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Leads Export");

            // Create header row
            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            for (int i = 0; i < columnsToExport.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columnsToExport.get(i).getDisplayName());
                cell.setCellStyle(headerStyle);
            }

            // Create data rows
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy HH:mm");
            int rowNum = 1;
            for (LeadDTO lead : leadsToExport) {
                Row row = sheet.createRow(rowNum++);
                for (int i = 0; i < columnsToExport.size(); i++) {
                    Cell cell = row.createCell(i);
                    Object value = getColumnValue(lead, columnsToExport.get(i), formatter);
                    if (value != null) {
                        if (value instanceof Number) {
                            cell.setCellValue(((Number) value).doubleValue());
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                }
            }

            // Auto-size columns
            for (int i = 0; i < columnsToExport.size(); i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new AppRTException("Failed to generate Excel export: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private List<LeadDTO> getLeadsForExport(LeadFilterCriteria criteria) {
        // If specific leads are selected, export only those
        if (criteria.getSelectedLeads() != null && !criteria.getSelectedLeads().isEmpty()) {
            List<Lead> selectedLeadEntities = criteria.getSelectedLeads().stream()
                .map(phone -> leadsRepository.getLeadByPhone(phone))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
            return selectedLeadEntities.stream()
                .map(this::fromSingleLeadToSingleLeadDTO)
                .collect(Collectors.toList());
        } else {
            // Use filter criteria to get leads from database
            criteria.setPage(0);
            criteria.setSize(Integer.MAX_VALUE);
            PaginatedLeadsResponse response = getLeadsWithFilters(criteria);
            return response.getLeads();
        }
    }

    private List<ExportColumn> getColumnsToExport(List<String> selectedColumns) {
        if (selectedColumns == null || selectedColumns.isEmpty()) {
            // Return all columns if none specified
            return Arrays.asList(ExportColumn.values());
        }

        return selectedColumns.stream()
                .map(ExportColumn::fromFieldName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Object getColumnValue(LeadDTO lead, ExportColumn column, DateTimeFormatter formatter) {
        switch (column) {
            case COMPANY_NAME:
                return lead.getCompanyName() != null ? lead.getCompanyName() : "";
            case LEAD_NAME:
                return lead.getLeadName() != null ? lead.getLeadName() : "";
            case PHONE:
                return lead.getPhone() != null ? lead.getPhone() : "";
            case EMAIL:
                return lead.getEmail() != null ? lead.getEmail() : "";
            case ADDRESS:
                return lead.getAddress() != null ? lead.getAddress() : "";
            case STATUS:
                return lead.getStatus() != null ? lead.getStatus().toString() : "";
            case APPLICATION_COUNT:
                return lead.getApplicationCount();
            case CREATED_DATE:
                return lead.getCreatedDate() != null ? lead.getCreatedDate().format(formatter) : "";
            case ASSIGNED_AGENTS:
                return lead.getAssignedAgents().stream()
                        .map(SalesAgentDTO::getFullName)
                        .collect(Collectors.joining(", "));
            default:
                return "";
        }
    }

    private List<LeadDTO> filterLeads(List<LeadDTO> leads, String search, String agentFilter, String typeFilter) {
        return leads.stream()
                .filter(lead -> {
                    // Search filter
                    boolean matchesSearch = search == null || search.trim().isEmpty() ||
                            (lead.getCompanyName() != null && lead.getCompanyName().toLowerCase().contains(search.toLowerCase())) ||
                            (lead.getLeadName() != null && lead.getLeadName().toLowerCase().contains(search.toLowerCase())) ||
                            (lead.getEmail() != null && lead.getEmail().toLowerCase().contains(search.toLowerCase()));

                    // Agent filter
                    boolean matchesAgent = agentFilter == null || agentFilter.trim().isEmpty() ||
                            lead.getAssignedAgents().stream()
                                    .anyMatch(agent -> agent.getFullName().equals(agentFilter));

                    // Type filter (status filter)
                    boolean matchesType = typeFilter == null || typeFilter.equals("all") ||
                            (lead.getStatus() != null && lead.getStatus().toString().toLowerCase().equals(typeFilter.toLowerCase()));

                    return matchesSearch && matchesAgent && matchesType;
                })
                .collect(Collectors.toList());
    }

    // Helper methods for converting entities to DTOs
    private LeadDTO fromSingleLeadToSingleLeadDTO(com.skillsync.applyr.core.models.entities.Lead raw) {
        LeadDTO lead = new LeadDTO();
        lead.setCompanyName(raw.getCompanyName());
        lead.setLeadName(raw.getLeadName());
        lead.setPhone(raw.getPhone());
        lead.setEmail(raw.getEmail());
        lead.setAddress(raw.getAddress());
        lead.setStatus(raw.getStatus());
        lead.setCreatedDate(raw.getCreatedDate());

        List<Application> applications = applicationRepository.findAllByLeadPhone(raw.getPhone());
        int count = 0;
        for (Application application : applications) {
            lead.getApplications().add(new ApplicationDTO(application));
            count++;
        }
        lead.setApplicationCount(count);

        raw.getComments().forEach(comment -> lead.getComments().add(new CommentDTO(comment)));
        raw.getActivities().forEach(activity -> lead.getActivities().add(new ActivityDTO(activity)));

        raw.getAssignedAgents().forEach(agent -> {
            SalesAgentDTO salesAgentDTO = new SalesAgentDTO();
            salesAgentDTO.setFullName(agent.getFullName());
            salesAgentDTO.setUsername(agent.getUser().getUsername());
            lead.getAssignedAgents().add(salesAgentDTO);
        });
        return lead;
    }

    private List<LeadDTO> fromLeadsToLeadDTOs(List<Lead> allLeadsRaw) {
        List<LeadDTO> leads = new ArrayList<>();
        for (Lead raw : allLeadsRaw) {
            leads.add(fromSingleLeadToSingleLeadDTO(raw));
        }
        Collections.reverse(leads);
        return leads;
    }

    public LeadStatsDTO getLeadStats(LeadFilterCriteria criteria) {
        // Get all leads that match the filter criteria (without pagination)
        LeadFilterCriteria statsCriteria = new LeadFilterCriteria();
        statsCriteria.setSearchTerm(criteria.getSearchTerm());
        statsCriteria.setAgentFilter(criteria.getAgentFilter());
        statsCriteria.setLeadTypeFilter(criteria.getLeadTypeFilter());
        statsCriteria.setStatusFilter(criteria.getStatusFilter());
        statsCriteria.setApplicationFilter(criteria.getApplicationFilter());
        statsCriteria.setPage(0);
        statsCriteria.setSize(Integer.MAX_VALUE); // Get all matching leads

        PaginatedLeadsResponse allMatchingLeads = getLeadsWithFilters(statsCriteria);
        List<LeadDTO> leads = allMatchingLeads.getLeads();

        // Calculate statistics
        int totalLeads = leads.size();
        int leadsWithApplications = (int) leads.stream()
                .filter(lead -> lead.getApplicationCount() > 0)
                .count();

        // Calculate status counts
        Map<String, Integer> statusCounts = new HashMap<>();
        statusCounts.put("HOT", 0);
        statusCounts.put("WARM", 0);
        statusCounts.put("COLD", 0);
        statusCounts.put("FRESH", 0);
        statusCounts.put("CLOSED", 0);

        for (LeadDTO lead : leads) {
            String status = lead.getStatus() != null ? lead.getStatus().toString() : "UNKNOWN";
            statusCounts.put(status, statusCounts.getOrDefault(status, 0) + 1);
        }

        return new LeadStatsDTO(totalLeads, leadsWithApplications, statusCounts);
    }

    public LeadStatsDTO getAgentLeadStats(LeadFilterCriteria criteria) {
        String username = UserUtils.getUsernameFromToken();
        Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(username);

        if (salesAgent.isEmpty()) {
            throw new AppRTException("Unable to fetch agent information", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // Set the agent filter to current agent to restrict results
        criteria.setAgentFilter(salesAgent.get().getFullName());

        // Use the existing stats method
        return getLeadStats(criteria);
    }
}
