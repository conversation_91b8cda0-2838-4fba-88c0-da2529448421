import React from "react";
import ReactDOM from "react-dom/client";
import { create<PERSON><PERSON>er<PERSON><PERSON>er, RouterProvider } from "react-router-dom";
import App from "./App.jsx";
import "./index.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Login from "./auth/login/Login.jsx";
import Signup from "./auth/register/SignupStudent.jsx";
import Home from "./layout/Home.jsx";
import Main from "./layout/Main.jsx";
import OverviewLayout from "./layout/OverviewLayout.jsx";
import Overview from "./pages/Student/Overview.jsx";
import { Provider } from "react-redux";
import { store } from "./app/store.js";
import SignUpCard from "./components/signupCard/SignUpCard.jsx";
import AgenId, { AccessDenied } from "./pages/Agent/AgentId.jsx";
import { IdentificationDocuments } from "./pages/student/DocumentsRegister.jsx";
import SuccessCard from "./components/successCard/SuccessCard.jsx";
import SignupStudent from "./auth/register/SignupStudent.jsx";
import AgentSignUp from "./auth/register/AgentSignUp.jsx";
import AgentLayout from "./layout/AgentLayout.jsx";
import AgentDashboard from "./pages/agent/AgentDashboard.jsx";
import routes from "./routes";
import ResumeRegister from "./pages/student/ResumeRegister.jsx";
import EducationCertReg from "./pages/student/EducationCertReg.jsx";
import AdminLayout from "./layout/AdminLayout.jsx";
import SourceOfTruthLayout from "./layout/SourceOfTruthLayout.jsx";
import AdminDashboard from "./pages/admin/dashboard/AdminDashboard.jsx";
import Employees from "./pages/admin/employees/Employees.jsx";
import EmployeeProfile from "./pages/admin/employeeProfile/EmployeeProfile.jsx";
import Qualifications from "./pages/admin/qualifications/Qualifications.jsx";
import AllProvidersScreen from "./pages/admin/provider/AllProviders.jsx";
import AllApplicationsScreen from "./pages/admin/applications/ApplicationsScreen.jsx";
import EmailTemplates from "./pages/emailtemplates/EmailTemplates.jsx";
import EmployeeActivity from "./pages/admin/employeeActivity/EmployeeActivity.jsx";
import LeadsPage from "./pages/admin/leads/leads.jsx";
import ExportLeadsPage from "./pages/admin/export-leads/ExportLeadsPage.jsx";
import Application from "./pages/admin/applications/Application.jsx";
import MarketTrend from "./pages/admin/market/MarketTrend.jsx";
import ApplicantTracker from "./pages/student/ApplicantTracker.jsx";
import AgentQualifications from "./pages/agent/AgentQualifications.jsx";
import AgentAllApplicationsScreen from "./pages/agent/AgentApplications.jsx";
import AgentLeadsPage from "./pages/agent/AgentLeads.jsx";
import AgentMonitoringTool from "./pages/agent/AgentMonitoringTool.jsx";
import LeadProfileWithTabs from "./pages/admin/leads/LeadProfile.jsx";
import Settings from "./pages/admin/settings/Settings.jsx";
import { TimeTrackerProvider } from "./services/TimeTrackerContext.jsx";
import TimeTracker from "./pages/admin/timetracker/TimeTracker.jsx";
import WeeklyTarget from "./pages/admin/weeklytarget/WeeklyTarget.jsx";
import AgentSettings from "./pages/agent/AgentSettings.jsx";
import AgentLeadProfile from "./pages/agent/AgentLeadProfile.jsx";
import ApplicationProfile from "./pages/admin/applications/ApplicationProfile.jsx";
import TroubleShoot from "./pages/admin/troubleshoot/TroubleShoot.jsx";
import TroubleShootAgent from "./pages/agent/TroubleShootAgent.jsx";
import XeroIntegration from "./pages/admin/financialDashboard/XeroIntegration.jsx";
import ProtectedRoute from "./components/auth/ProtectedRoute.jsx";
import TestProtectedRoutes from "./components/auth/TestProtectedRoutes.jsx";
import AdminSelectionScreen from "./pages/admin/AdminSelectionScreen.jsx";

// Source of Truth pages
import SourceOfTruthDashboard from "./pages/admin/sourceoftruth/dashboard/SourceOfTruthDashboard.jsx";
import XeroImportPage from "./pages/admin/sourceoftruth/xero/XeroImportPage.jsx";
import WeeklyTargetsPage from "./pages/admin/sourceoftruth/weeklytargets/WeeklyTargetsPage.jsx";
import TimeTrackingPage from "./pages/admin/sourceoftruth/timetracking/TimeTrackingPage.jsx";

import OperationsLayout from "./layout/OperationsLayout.jsx";
import FileStatus from "./pages/operations/fiilestatus/FileStatus.jsx";
import FileStatusDetail from "./pages/operations/fiilestatus/FileStatusDetail.jsx";
import AdminFileStatus from "./pages/admin/filestatus/FileStatus.jsx";
import AdminFileStatusDetail from "./pages/admin/filestatus/FileStatusDetail.jsx";

// New Operations Components
import OperationsDashboard from "./pages/operations/dashboard/OperationsDashboard.jsx";
import QuoteRequests from "./pages/operations/quoteRequests/QuoteRequests.jsx";
import DraftedQuotes from "./pages/operations/draftedQuotes/DraftedQuotes.jsx";
import OperationsApplications from "./pages/operations/applications/OperationsApplications.jsx";
import RTOManagement from "./pages/admin/rto/RTOManagement.jsx";
import RTOProfile from "./pages/admin/rto/RTOProfile.jsx";
import OperationsRTOManagement from "./pages/operations/rto/OperationsRTOManagement.jsx";
import OperationsRTOProfile from "./pages/operations/rto/OperationsRTOProfile.jsx";
import OperationsSettings from "./pages/operations/settings/OperationsSettings.jsx";
import OperationsQualifications from "./pages/operations/qualifications/OperationsQualifications.jsx";
import WhatsAppMarketing from "./pages/whatsapp/WhatsAppMarketing.jsx";
import AdminExternalCommissions from "./pages/admin/commissions/AdminExternalCommissions.jsx";
import OperationsExternalCommissions from "./pages/operations/commissions/OperationsExternalCommissions.jsx";
import AgentExternalCommissions from "./pages/agent/commissions/AgentExternalCommissions.jsx";
import AgentRTOProfile from "./pages/agent/AgentRTOProfile.jsx";


const router = createBrowserRouter([
  {
    path: routes.home,
    element: <Main />,
    children: [
      {
        path: routes.home,
        element: <Login />,
      },
      {
        path: routes.login,
        element: <Login />,
      },

      {
        path: routes.signupStudent,
        element: <SignupStudent />,
      },
      {
        path: routes.signupAgent,
        element: <AgentSignUp />,
      },
      {
        path: routes.signupSelection,
        element: <SignUpCard />,
      },

      {
        path: routes.connectWithAgent,
        element: <AgenId />,
      },
      {
        path: routes.accessDenied,
        element: <AccessDenied />,
      },
      {
        path: routes.uploadIdentification,
        element: <IdentificationDocuments />,
      },

      {
        path: routes.uploadResumeRegister,
        element: <ResumeRegister></ResumeRegister>,
      },
      {
        path: routes.uploadCertificatesRegister,
        element: <EducationCertReg />,
      },
      {
        path: routes.successCard,
        element: <SuccessCard />,
      },
    ],
  },
  {
    path: routes.applicationStatus,
    element: <ApplicantTracker />,
  },
  // Student part
  {
    path: routes.student,
    element: <OverviewLayout />,
    children: [
      {
        path: routes.studentDashboard,
        element: <Overview />,
      },
    ],
  },
  // Operations part
  {
    path: routes.operationsLayout,
    element: <ProtectedRoute allowedRoles={["ROLE_OPERATIONS", "ROLE_ADMIN"]} />,
    children: [
      {
        path: "",
        element: <OperationsLayout />,
        children: [
          {
            path: routes.operationsDashboard.replace(routes.operationsLayout + "/", ""),
            element: <OperationsDashboard />,
          },
          {
            path: routes.operationsQuoteRequests.replace(routes.operationsLayout + "/", ""),
            element: <QuoteRequests />,
          },
          {
            path: routes.operationsDraftedQuotes.replace(routes.operationsLayout + "/", ""),
            element: <DraftedQuotes />,
          },
          {
            path: routes.operationsApplications.replace(routes.operationsLayout + "/", ""),
            element: <OperationsApplications />,
          },
          {
            path: routes.operationsFileStatus.replace(routes.operationsLayout + "/", ""),
            element: <FileStatus />,
          },
          {
            path: routes.operationsQualifications.replace(routes.operationsLayout + "/", ""),
            element: <OperationsQualifications />,
          },
          {
            path: routes.operationsSettings.replace(routes.operationsLayout + "/", ""),
            element: <OperationsSettings />,
          },
          {
            path: routes.operationsRTOManagement.replace(routes.operationsLayout + "/", ""),
            element: <OperationsRTOManagement />,
          },
          {
            path: routes.operationsRTOProfile.replace(routes.operationsLayout + "/", ""),
            element: <OperationsRTOProfile />,
          },
          {
            path: routes.operationsFileStatusDetail.replace(routes.operationsLayout + "/", ""),
            element: <FileStatusDetail />,
          },
          {
            path: routes.operationsExternalCommissions.replace(routes.operationsLayout + "/", ""),
            element: <OperationsExternalCommissions />,
          },
        ],
      }
    ],
  },
  //agent
  {
    path: routes.agentLayout,
    element: <ProtectedRoute allowedRoles={["ROLE_SALES", "ROLE_ADMIN"]} />,
    children: [
      {
        path: "",
        element: <AgentLayout />,
        children: [
          {
            path: routes.agentDashboard.replace(routes.agentLayout + "/", ""),
            element: <AgentDashboard />,
          },
          {
            path: routes.agentQualifications.replace(routes.agentLayout + "/", ""),
            element: <AgentQualifications />,
          },
          {
            path: routes.agentApplications.replace(routes.agentLayout + "/", ""),
            element: <AgentAllApplicationsScreen />,
          },
          {
            path: routes.agentLeads.replace(routes.agentLayout + "/", ""),
            element: <AgentLeadsPage />,
          },
          {
            path: routes.agentMonitoringTool.replace(routes.agentLayout + "/", ""),
            element: <AgentMonitoringTool />,
          },
          {
            path: routes.agentLeadsProfile.replace(routes.agentLayout + "/", ""),
            element: <AgentLeadProfile />,
          },
          {
            path: routes.agentSettings.replace(routes.agentLayout + "/", ""),
            element: <AgentSettings />,
          },
          {
            path: routes.agentApplicationProfile.replace(routes.agentLayout + "/", ""),
            element: <ApplicationProfile />,
          },
          {
            path: routes.agentTroubleshoot.replace(routes.agentLayout + "/", ""),
            element: <TroubleShootAgent />,
          },
          {
            path: routes.agentWhatsAppMarketing.replace(routes.agentLayout + "/", ""),
            element: <WhatsAppMarketing />,
          },
          {
            path: routes.agentExternalCommissions.replace(routes.agentLayout + "/", ""),
            element: <AgentExternalCommissions />,
          },
          {
            path: routes.agentRTOProfile.replace(routes.agentLayout + "/", ""),
            element: <AgentRTOProfile />,
          },
        ],
      }
    ],
  },
  // Admin Selection Screen
  {
    path: routes.adminSelection,
    element: <ProtectedRoute allowedRoles={["ROLE_ADMIN"]} />,
    children: [
      {
        path: "",
        element: <AdminSelectionScreen />,
      },
    ],
  },

  //admin
  {
    path: routes.adminLayout,
    element: <ProtectedRoute allowedRoles={["ROLE_ADMIN"]} />,
    children: [
      // Source of Truth Layout
      {
        path: routes.sourceOfTruthLayout.replace(routes.adminLayout + "/", ""),
        element: <SourceOfTruthLayout />,
        children: [
          {
            path: "dashboard",
            element: <SourceOfTruthDashboard />,
          },
          {
            path: "xero-import",
            element: <XeroImportPage />,
          },
          {
            path: "weekly-target",
            element: <WeeklyTargetsPage />,
          },
          {
            path: "time-tracking",
            element: <TimeTrackingPage />,
          },
        ],
      },
      // Admin Layout
      {
        path: "",
        element: <AdminLayout />,
        children: [
          {
            path: routes.adminDashboard.replace(routes.adminLayout + "/", ""),
            element: <AdminDashboard />,
          },
          {
            path: routes.adminLeads.replace(routes.adminLayout + "/", ""),
            element: <LeadsPage />,
          },
          {
            path: routes.adminExportLeads.replace(routes.adminLayout + "/", ""),
            element: <ExportLeadsPage />,
          },
          {
            path: routes.leadProfile.replace(routes.adminLayout + "/", ""),
            element: <LeadProfileWithTabs />,
          },
          {
            path: routes.applicationProfile.replace(routes.adminLayout + "/", ""),
            element: <ApplicationProfile />,
          },
          {
            path: routes.adminEmployees.replace(routes.adminLayout + "/", ""),
            element: <Employees />,
          },
          {
            path: routes.EmployeeProfile.replace(routes.adminLayout + "/", ""),
            element: <EmployeeProfile />,
          },
          {
            path: routes.qualifications.replace(routes.adminLayout + "/", ""),
            element: <Qualifications />,
          },
          {
            path: routes.providers.replace(routes.adminLayout + "/", ""),
            element: <AllProvidersScreen />,
          },
          {
            path: routes.applications.replace(routes.adminLayout + "/", ""),
            element: <AllApplicationsScreen />,
          },
          {
            path: routes.emailtemplates.replace(routes.adminLayout + "/", ""),
            element: <EmailTemplates />,
          },
          {
            path: routes.employeeActivity.replace(routes.adminLayout + "/", ""),
            element: <EmployeeActivity />,
          },
          {
            path: routes.studentApplication.replace(routes.adminLayout + "/", ""),
            element: <Application />,
          },
          {
            path: routes.adminMarketTrend.replace(routes.adminLayout + "/", ""),
            element: <MarketTrend />,
          },
          {
            path: routes.adminSettings.replace(routes.adminLayout + "/", ""),
            element: <Settings />,
          },
          {
            path: routes.timetracker.replace(routes.adminLayout + "/", ""),
            element: <TimeTracker />,
          },
          {
            path: routes.weeklytarget.replace(routes.adminLayout + "/", ""),
            element: <WeeklyTarget />,
          },
          {
            path: routes.troubleshoot.replace(routes.adminLayout + "/", ""),
            element: <TroubleShoot />,
          },
          {
            path: routes.adminXeroIntegration.replace(routes.adminLayout + "/", ""),
            element: <XeroIntegration />,
          },
          {
            path: routes.adminWhatsAppMarketing.replace(routes.adminLayout + "/", ""),
            element: <WhatsAppMarketing />,
          },
          {
            path: routes.adminQuoteRequests.replace(routes.adminLayout + "/", ""),
            element: <QuoteRequests />,
          },
          {
            path: routes.adminDraftedQuotes.replace(routes.adminLayout + "/", ""),
            element: <DraftedQuotes />,
          },
          {
            path: routes.adminOperationsApplications.replace(routes.adminLayout + "/", ""),
            element: <OperationsApplications />,
          },
          {
            path: routes.adminRTOManagement.replace(routes.adminLayout + "/", ""),
            element: <RTOManagement />,
          },
          {
            path: routes.adminRTOProfile.replace(routes.adminLayout + "/", ""),
            element: <RTOProfile />,
          },
          {
            path: routes.adminFileStatus.replace(routes.adminLayout + "/", ""),
            element: <AdminFileStatus />,
          },
          {
            path: routes.adminFileStatusDetail.replace(routes.adminLayout + "/", ""),
            element: <AdminFileStatusDetail />,
          },
          {
            path: routes.adminExternalCommissions.replace(routes.adminLayout + "/", ""),
            element: <AdminExternalCommissions />,
          },
        ],
      }
    ],
  },
]);

ReactDOM.createRoot(document.getElementById("root")).render(
  <Provider store={store}>
    <TimeTrackerProvider>
      <React.StrictMode>
        <RouterProvider router={router} />
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </React.StrictMode>
    </TimeTrackerProvider>
  </Provider>
);
