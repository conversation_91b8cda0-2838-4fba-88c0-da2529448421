import AgentSettings from "./pages/agent/AgentSettings";

const routes = {
  home: "/",
  login: "/signin",
  signupStudent: "/signup/student",
  signupAgent: "/signup/agent",
  signupSelection: "/signup",
  connectWithAgent: "/connect",
  uploadIdentification: "/upload/100points",
  uploadResumeRegister: "/upload/resumeregister",
  uploadCertificatesRegister: "/upload/certificateuploadreg",
  uploadResume: "/upload/resume",
  resume: "/resume",
  successCard: "/successcard",
  accessDenied: "/access-denied",
  applicationStatus: "/application/status",

  student: "/student",
  studentDashboard: "/student/dashboard",
  studentApplication: "/admin/application",

  agentLayout: "/agent",
  agentDashboard: "/agent/dashboard",
  agentQualifications: "/agent/qualifications",
  agentApplications: "/agent/applications",
  agentLeads: "/agent/leads",
  agentLeadsProfile: "/agent/lead/profile/:phoneNumber",
  agentSettings: "/agent/settings",
  agentApplicationProfile: "/agent/application/profile/:applicationId",
  agentTroubleshoot: "/agent/troubleshoot",
  agentWhatsAppMarketing: "/agent/whatsapp-marketing",
  agentExternalCommissions: "/agent/commissions",
  agentMonitoringTool: "/agent/monitoring-tool",
  agentRTOProfile: "/agent/rto/:rtoCode",

  // Admin selection screen after login
  adminSelection: "/admin/selection",

  adminLayout: "/admin",
  adminDashboard: "/admin/dashboard",
  adminEmployees: "/admin/employees",
  adminLeads: "/admin/leads",
  adminExportLeads: "/admin/export-leads",
  EmployeeProfile: "/admin/employeeprofile",
  qualifications: "/admin/qualifications",
  providers: "/admin/providers",
  applications: "/admin/applications",
  emailtemplates: "/admin/emailtemplates",
  employeeActivity: "/admin/employeeactivity",
  adminMarketTrend: "/admin/markettrends",
  adminSettings: "/admin/settings",
  adminXeroIntegration: "/admin/xero/integration",
  adminWhatsAppMarketing: "/admin/whatsapp-marketing",
  adminQuoteRequests: "/admin/quoterequests",
  adminDraftedQuotes: "/admin/draftedquotes",
  adminOperationsApplications: "/admin/operationsapplications",
  adminRTOManagement: "/admin/rto",
  adminRTOProfile: "/admin/rto/:rtoCode",
  adminFileStatus: "/admin/filestatus",
  adminFileStatusDetail: "/admin/filestatus/:applicationId/:qualificationId",
  adminExternalCommissions: "/admin/commissions",

  // leadsPage: "/admin/lead/profile",
  leadProfile: "/admin/lead/profile/:phoneNumber",
  applicationProfile: "/admin/application/profile/:applicationId",
  timetracker: "/admin/timetracker",
  weeklytarget: "/admin/weeklytarget",
  troubleshoot: "/admin/troubleshoot",

  // Source of Truth routes
  sourceOfTruthLayout: "/admin/sourceoftruth",
  sourceOfTruthDashboard: "/admin/sourceoftruth/dashboard",
  sourceOfTruthXeroImport: "/admin/sourceoftruth/xero-import",
  sourceOfTruthWeeklyTarget: "/admin/sourceoftruth/weekly-target",
  sourceOfTruthTimeTracking: "/admin/sourceoftruth/time-tracking",


  //operations
  operationsLayout: "/operations",
  operationsDashboard: "/operations/dashboard",
  operationsQuoteRequests: "/operations/quoterequests",
  operationsDraftedQuotes: "/operations/draftedquotes",
  operationsApplications: "/operations/applications",
  operationsFileStatus: "/operations/filestatus",
  operationsQualifications: "/operations/qualifications",
  operationsSettings: "/operations/settings",
  operationsRTOManagement: "/operations/rto",
  operationsRTOProfile: "/operations/rto/:rtoCode",
  operationsFileStatusDetail: "/operations/filestatus/:applicationId/:qualificationId",
  operationsExternalCommissions: "/operations/commissions",

};

export default routes;
