package com.skillsync.applyr.modules.company.controller;


import com.skillsync.applyr.core.models.enums.LeadsStatus;
import com.skillsync.applyr.core.models.enums.PaidStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.core.models.enums.CommissionPaymentStatus;
import com.skillsync.applyr.core.models.enums.FileStatus;
import com.skillsync.applyr.core.models.enums.RTOPaymentStatus;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.authentication.models.ChangePassDTO;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.models.ExternalCommissionRequestDTO;
import com.skillsync.applyr.modules.company.models.ExternalCommissionResponseDTO;
import com.skillsync.applyr.modules.company.services.ApplicationFileServices;
import com.skillsync.applyr.modules.company.services.CompanyServices;
import com.skillsync.applyr.modules.company.services.RTOServices;
import com.skillsync.applyr.modules.sales.models.QuoteRequestDTO;
import com.skillsync.applyr.modules.sales.models.RaiseQuoteAndInvoiceDTO;
import com.skillsync.applyr.modules.sales.models.RequestStatusChangeDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/generic")
@PreAuthorize("hasRole('ADMIN') AND hasRole('SALES') AND hasRole('OPERATIONS')")
public class GenericController {
    private final CompanyServices companyServices;
    private final RTOServices rtoServices;
    private final ApplicationFileServices applicationFileServices;

    public GenericController(CompanyServices companyServices, RTOServices rtoServices, ApplicationFileServices applicationFileServices) {
        this.companyServices = companyServices;
        this.rtoServices = rtoServices;
        this.applicationFileServices = applicationFileServices;
    }

    @GetMapping("/profile")
    public ResponseEntity<ProfileDTO> getProfile() {
        return ResponseEntity.ok(companyServices.getProfile());
    }

    @GetMapping("/agent/profile")
    public ResponseEntity<ProfileDTO> getCurrentAgentProfile() {
        return ResponseEntity.ok(companyServices.getAgentProfile());
    }

    @PostMapping("/leads/upload")
    public SuccessResponse uploadLeads(@RequestParam("file") MultipartFile file, @RequestParam(value = "assigned", required = false) List<String> assigned) throws Exception {
        return companyServices.uploadLeadsFromCSV(file, assigned);
    }

    @PostMapping("/leads/single")
    public SuccessResponse uploadSingleLead(@RequestBody LeadDTO leadDTO) throws Exception {
        return companyServices.uploadSingleLead(leadDTO);
    }

    @GetMapping("/leads")
    public ResponseEntity<List<LeadDTO>> getLeads() {
        return ResponseEntity.ok(companyServices.getAllLeads());
    }

    @PostMapping("/leads/paginated")
    public ResponseEntity<PaginatedLeadsResponse> getLeadsWithFilters(@RequestBody LeadFilterCriteria criteria) {
        return ResponseEntity.ok(companyServices.getLeadsWithFilters(criteria));
    }


    @GetMapping("/lead/{phoneNumber}")
    public ResponseEntity<LeadDTO> getSingleLeads(@PathVariable String phoneNumber) throws Exception {
        return ResponseEntity.ok(companyServices.getSingleLead(phoneNumber));
    }

    @PutMapping("/update/password")
    public ResponseEntity<SuccessResponse> changePassword(@RequestBody ChangePassDTO changePassDTO) {
        return ResponseEntity.ok(companyServices.changePassword(changePassDTO));
    }

    @GetMapping("/agent/leads")
    public ResponseEntity<List<LeadDTO>> getLeadsOfAgent() {
        return ResponseEntity.ok(companyServices.getAllLeadsOfAgent());
    }

    @PostMapping("/agent/leads/filtered")
    public ResponseEntity<PaginatedLeadsResponse> getAgentLeadsWithFilters(@RequestBody LeadFilterCriteria criteria) {
        return ResponseEntity.ok(companyServices.getAgentLeadsWithFilters(criteria));
    }

    @PostMapping("/leads/stats")
    public ResponseEntity<LeadStatsDTO> getLeadStats(@RequestBody LeadFilterCriteria criteria) {
        return ResponseEntity.ok(companyServices.getLeadStats(criteria));
    }

    @PostMapping("/agent/leads/stats")
    public ResponseEntity<LeadStatsDTO> getAgentLeadStats(@RequestBody LeadFilterCriteria criteria) {
        return ResponseEntity.ok(companyServices.getAgentLeadStats(criteria));
    }

    @PostMapping("/leads/export")
    public ResponseEntity<byte[]> exportLeadsWithFilters(@RequestBody LeadFilterCriteria criteria) {
        byte[] csvData = companyServices.exportLeadsToCSV(criteria);

        String filename = "leads-export-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MMM-dd")) + ".csv";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("text/csv"));
        headers.setContentDispositionFormData("attachment", filename);
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        return ResponseEntity.ok()
                .headers(headers)
                .body(csvData);
    }

    @PostMapping("/leads/export-enhanced")
    public ResponseEntity<byte[]> exportLeadsEnhanced(@RequestBody ExportRequestDTO exportRequest) {
        byte[] exportData = companyServices.exportLeads(exportRequest);

        String format = exportRequest.getFormat() != null ? exportRequest.getFormat().toLowerCase() : "csv";
        String extension = "excel".equals(format) ? "xlsx" : "csv";
        String mimeType = "excel".equals(format) ?
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "text/csv";

        String filename = "leads-export-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MMM-dd")) + "." + extension;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(mimeType));
        headers.setContentDispositionFormData("attachment", filename);
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        return ResponseEntity.ok()
                .headers(headers)
                .body(exportData);
    }

    @GetMapping("/leads/export")
    public ResponseEntity<byte[]> exportLeads(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String agentFilter,
            @RequestParam(required = false) String typeFilter,
            @RequestParam(required = false) List<String> selectedLeads) {

        byte[] csvData = companyServices.exportLeadsToCSV(search, agentFilter, typeFilter, selectedLeads);

        String filename = "leads-export-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MMM-dd")) + ".csv";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("text/csv"));
        headers.setContentDispositionFormData("attachment", filename);
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        return ResponseEntity.ok()
                .headers(headers)
                .body(csvData);
    }

    @GetMapping("/agent/dashboard/{startDate}/{endDate}")
    public ResponseEntity<DashboardDTO> getAgentDashboard(
            @PathVariable LocalDateTime startDate,
            @PathVariable LocalDateTime endDate,
            @RequestParam(required = false) Long targetId,
            @RequestParam(required = false) List<Long> targetIds) {
        if (targetIds != null && !targetIds.isEmpty()) {
            return ResponseEntity.ok(companyServices.getAgentDashboardByMultipleTargetIds(targetIds));
        }
        if (targetId != null) {
            return ResponseEntity.ok(companyServices.getAgentDashboardByTargetId(targetId));
        }
        return ResponseEntity.ok(companyServices.getAgentDashboard(startDate, endDate));
    }

    @GetMapping("/agent/dashboard/this-month")
    public ResponseEntity<DashboardDTO> getAgentDashboardThisMonth() {
        return ResponseEntity.ok(companyServices.getAgentDashboardThisMonth());
    }

    @GetMapping("/agent/dashboard/this-year")
    public ResponseEntity<DashboardDTO> getAgentDashboardThisYear() {
        return ResponseEntity.ok(companyServices.getAgentDashboardThisYear());
    }


    @PostMapping("/applications")
    public ResponseEntity<ApplicationResponseDTO> addApplication(@RequestBody ApplicationRequestDTO application) {
        return ResponseEntity.ok(companyServices.addApplications(application));
    }

    @GetMapping("/applications")
    public ResponseEntity<List<ApplicationResponseDTO>> getApplications() {
        return ResponseEntity.ok(companyServices.getAllApplicationOfAgent());
    }

    @GetMapping("/application/{applicationId}")
    public ResponseEntity<ApplicationResponseDTO> getSingleApplication(@PathVariable String applicationId) {
        return ResponseEntity.ok(companyServices.getSingleApplications(applicationId));
    }

    @GetMapping("/applications/all")
    public ResponseEntity<List<ApplicationResponseDTO>> getAllApplications() {
        return ResponseEntity.ok(companyServices.getAllApplication());
    }


    @PutMapping("/applications/{applicationId}/status")
    public ResponseEntity<SuccessResponse> updateApplicationStatus(@PathVariable String applicationId,@RequestBody Map<String, String> body) {
        String statusStr = body.get("status");
        Status newStatus = Status.valueOf(statusStr);
        return ResponseEntity.ok(companyServices.updateApplicationStatus(applicationId, newStatus));
    }

    @PutMapping("/payment/{applicationId}/status")
    public ResponseEntity<SuccessResponse> updateApplicationPaymentStatus(@PathVariable String applicationId,@RequestBody Map<String, String> body) {
        String statusStr = body.get("status");
        PaidStatus newStatus = PaidStatus.valueOf(statusStr);
        return ResponseEntity.ok(companyServices.updateApplicationPaymentStatus(applicationId, newStatus));
    }

    @PutMapping("/applications/{applicationId}/created-date")
    public ResponseEntity<SuccessResponse> updateApplicationCreatedDate(@PathVariable String applicationId, @RequestBody Map<String, String> body) {
        LocalDateTime newCreatedDate = LocalDateTime.parse(body.get("createdDate"));
        return ResponseEntity.ok(companyServices.updateApplicationCreatedDate(applicationId, newCreatedDate));
    }

    @PutMapping("/payment/{applicationId}/partial")
    public ResponseEntity<SuccessResponse> updatePaidAmount(@PathVariable String applicationId,@RequestBody Map<String, String> body) {
        double paidAmount = Double.parseDouble(body.get("paid"));
        return ResponseEntity.ok(companyServices.updateApplicationPaymentAmount(applicationId, paidAmount));
    }

    @PutMapping("/applications/{applicationId}/quote")
    public ResponseEntity<SuccessResponse> updateQuoteRef(@PathVariable String applicationId,@RequestBody Map<String, String> body) {
        String quoteStr = body.get("quote");
        return ResponseEntity.ok(companyServices.updateApplicationQuoteRef(applicationId, quoteStr));
    }

    @PutMapping("/applications/{applicationId}/invoice")
    public ResponseEntity<SuccessResponse> updateInvoiceRef(@PathVariable String applicationId,@RequestBody Map<String, String> body) {
        String invoiceStr = body.get("invoice");
        return ResponseEntity.ok(companyServices.updateApplicationInvoiceRef(applicationId, invoiceStr));
    }

    @PutMapping("/applications/{applicationId}/qualification")
    public ResponseEntity<SuccessResponse> addQualificationToApplication(@PathVariable String applicationId, @RequestBody SoldQualificationDTO soldQualificationDTO) {
        return ResponseEntity.ok(companyServices.addSoldQualification(applicationId, soldQualificationDTO));
    }

    @PutMapping("/applications/{applicationId}/info")
    public ResponseEntity<SuccessResponse> updateApplicantInfo(@PathVariable String applicationId, @RequestBody ApplicantUpdateDTO updateDTO) {
        return ResponseEntity.ok(companyServices.updateApplicantInformation(applicationId, updateDTO));
    }

    @DeleteMapping("/applications/{applicationId}/{qualificationId}")
    public ResponseEntity<SuccessResponse> removeQualificationFromApplication(@PathVariable String applicationId, @PathVariable String qualificationId) {
        return ResponseEntity.ok(companyServices.removeSoldQualification(applicationId, qualificationId));
    }

    @DeleteMapping("/applications/{applicationId}")
    public ResponseEntity<SuccessResponse> deleteApplication(@PathVariable String applicationId) {
        return ResponseEntity.ok(companyServices.deleteApplication(applicationId));
    }

    @PutMapping("/lead/{leadPhone}/status")
    public ResponseEntity<SuccessResponse> updateLeadStatus(@PathVariable String leadPhone, @RequestBody Map<String, String> body) {
        String statusStr = body.get("status");
        LeadsStatus status = LeadsStatus.valueOf(statusStr);
        return ResponseEntity.ok(companyServices.changeLeadStatus(leadPhone, status));
    }

    @PutMapping("/lead/{leadPhone}/owner")
    public ResponseEntity<SuccessResponse> changeLeadOwner(@PathVariable String leadPhone,@RequestBody Map<String, String> body) {
        String newOwner = body.get("newOwner");
        return ResponseEntity.ok(companyServices.changeLeadOwner(leadPhone, newOwner));
    }

    @PutMapping("/lead/{leadPhone}/comment")
    public ResponseEntity<CommentDTO> addCommentToLead(@PathVariable String leadPhone, @RequestBody Map<String, String> body) {
        String comment = body.get("comment");
        CommentRequestDTO commentDTO = new CommentRequestDTO(comment, leadPhone);
        return ResponseEntity.ok(companyServices.addLeadComment(commentDTO));
    }

    @PutMapping("/lead/{leadPhone}/comment/{commentId}")
    public ResponseEntity<CommentDTO> updateLeadComment(
            @PathVariable String leadPhone,
            @PathVariable Long commentId,
            @RequestBody Map<String, String> body) {
        String newContent = body.get("content");
        return ResponseEntity.ok(companyServices.updateLeadComment(commentId, leadPhone, newContent));
    }

    @DeleteMapping("/lead/{leadPhone}/comment/{commentId}")
    public ResponseEntity<SuccessResponse> deleteLeadComment(
            @PathVariable String leadPhone,
            @PathVariable Long commentId) {
        return ResponseEntity.ok(companyServices.deleteLeadComment(commentId, leadPhone));
    }

    @PutMapping("/lead/edit")
    public ResponseEntity<SuccessResponse> updateLead(@RequestBody LeadUpdateDTO leadUpdateDTO) {
        return ResponseEntity.ok(companyServices.updateLead(leadUpdateDTO));
    }

    @DeleteMapping("/lead/{phone}")
    public ResponseEntity<SuccessResponse> deleteLead(@PathVariable String phone) {
        return ResponseEntity.ok(companyServices.deleteLead(phone));
    }

    @GetMapping("/time/update")
    public ResponseEntity<LocalDateTime> updateTimeStamp() {
        return ResponseEntity.ok(companyServices.updateTimeTracker());
    }

    @GetMapping("/tracker/today")
    public ResponseEntity<List<TrackerDataDTO>> getTodayTimeTracker() {
        return ResponseEntity.ok(companyServices.getAllTrackersOfToday());
    }

    @GetMapping("/tracker/{username}/{month}/{year}")
    public ResponseEntity<List<TrackerDataDTO>> getTrackersOfToday(@PathVariable String username, @PathVariable int month, @PathVariable int year) {
        return ResponseEntity.ok(companyServices.getTrackerOfUserByMonthYear(username, month, year));
    }

    @PostMapping("/target/create")
    public ResponseEntity<SuccessResponse> createTarget(@RequestBody WeeklyTargetsRequestDTO target) {
        return ResponseEntity.ok(companyServices.createTarget(target));
    }

    @PutMapping("/target/update")
    public ResponseEntity<SuccessResponse> updateTarget(@RequestBody WeeklyTargetsRequestDTO target) {
        return ResponseEntity.ok(companyServices.updateTarget(target));
    }

    @GetMapping("/target/all")
    public ResponseEntity<List<WeeklyTargetResponseDTO>> getAllTargets() {
        return ResponseEntity.ok(companyServices.getAllTargets());
    }


    @GetMapping("/quote/request/all")
    private ResponseEntity<List<QuoteRequestDTO>> getAllQuoteRequests() {
        return ResponseEntity.ok(companyServices.getAllQuoteRequests());
    }

    @GetMapping("/quote/request/drafted")
    private ResponseEntity<List<QuoteRequestDTO>> getDraftedQuoteRequests() {
        return ResponseEntity.ok(companyServices.getDraftedQuoteRequests());
    }

    @GetMapping("/quote/request/requests")
    private ResponseEntity<List<QuoteRequestDTO>> getRequestedQuoteRequests() {
        return ResponseEntity.ok(companyServices.getPendingQuoteRequests());
    }

    @PutMapping("/quote/request/status/edit")
    private ResponseEntity<SuccessResponse> changeQuoteRequestStatus(@RequestBody RequestStatusChangeDTO requestDTO) {
        return ResponseEntity.ok(companyServices.changeQuoteStatus(requestDTO));
    }

    @PutMapping("/invoice/request/status/edit")
    private ResponseEntity<SuccessResponse> changeInvoiceRequestStatus(@RequestBody RequestStatusChangeDTO requestDTO) {
        return ResponseEntity.ok(companyServices.changeInvoiceStatus(requestDTO));
    }

    @PutMapping("/application/raise/initial")
    private ResponseEntity<SuccessResponse> raiseQuoteAndInvoiceInitial(@RequestBody RaiseQuoteAndInvoiceDTO requestDTO) {
        return ResponseEntity.ok(companyServices.raiseQuoteAndInvoices(requestDTO));
    }

    // RTO endpoints
    @PostMapping("/rto/fetch/{rtoCode}")
    public ResponseEntity<SuccessResponse> fetchAndSaveRTO(@PathVariable String rtoCode) {
        return ResponseEntity.ok(rtoServices.fetchAndSaveRTOData(rtoCode));
    }

    @GetMapping("/rto/all")
    public ResponseEntity<List<RTODataDTO>> getAllRTOs() {
        return ResponseEntity.ok(rtoServices.getAllRTOs());
    }

    @GetMapping("/rto/{rtoCode}")
    public ResponseEntity<RTODataDTO> getRTOByCode(@PathVariable String rtoCode) {
        return ResponseEntity.ok(rtoServices.getRTOByCode(rtoCode));
    }

    @PostMapping("/rto/reload/{rtoCode}")
    public ResponseEntity<SuccessResponse> reloadRTOInfo(@PathVariable String rtoCode) {
        return ResponseEntity.ok(rtoServices.reloadRTOInfo(rtoCode));
    }

    @PutMapping("/rto/contact/{contactId}")
    public ResponseEntity<SuccessResponse> updateRTOContact(
            @PathVariable Long contactId,
            @RequestBody RTOContactDTO contactDTO) {
        return ResponseEntity.ok(rtoServices.updateRTOContact(contactId, contactDTO));
    }

    @PostMapping("/rto/{rtoCode}/contact")
    public ResponseEntity<SuccessResponse> addRTOContact(
            @PathVariable String rtoCode,
            @RequestBody RTOContactDTO contactDTO) {
        return ResponseEntity.ok(rtoServices.addRTOContact(rtoCode, contactDTO));
    }

    @DeleteMapping("/rto/{rtoCode}")
    public ResponseEntity<SuccessResponse> deleteRTO(@PathVariable String rtoCode) {
        return ResponseEntity.ok(rtoServices.deleteRTO(rtoCode));
    }

    @DeleteMapping("/rto/contact/{contactId}")
    public ResponseEntity<SuccessResponse> deleteRTOContact(@PathVariable Long contactId) {
        return ResponseEntity.ok(rtoServices.deleteRTOContact(contactId));
    }

    // External Commission endpoints
    @GetMapping("/commissions/all")
    public ResponseEntity<List<ExternalCommissionResponseDTO>> getAllCommissions() {
        return ResponseEntity.ok(applicationFileServices.getAllCommissions());
    }

    @PutMapping("/commission/{applicationId}/status")
    public ResponseEntity<SuccessResponse> updateCommissionStatus(
            @PathVariable String applicationId,
            @RequestBody Map<String, String> body) {
        String statusStr = body.get("status");
        CommissionPaymentStatus newStatus = CommissionPaymentStatus.valueOf(statusStr);
        return ResponseEntity.ok(applicationFileServices.changeCommissionStatus(applicationId, newStatus));
    }

    @PutMapping("/commission/{applicationId}/edit")
    public ResponseEntity<SuccessResponse> editCommission(
            @PathVariable String applicationId,
            @RequestBody ExternalCommissionRequestDTO commissionDTO) {
        return ResponseEntity.ok(applicationFileServices.editCommission(applicationId, commissionDTO));
    }

    @DeleteMapping("/commission/{applicationId}")
    public ResponseEntity<SuccessResponse> deleteCommission(@PathVariable String applicationId) {
        return ResponseEntity.ok(applicationFileServices.deleteCommission(applicationId));
    }

    @PostMapping("/commission/create")
    public ResponseEntity<SuccessResponse> createCommission(@RequestBody ExternalCommissionRequestDTO commissionDTO) {
        return ResponseEntity.ok(applicationFileServices.createExternalCommissionForApplication(commissionDTO));
    }

    // File status update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/file-status")
    public ResponseEntity<SuccessResponse> updateFileStatus(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        String statusStr = body.get("status");
        FileStatus newStatus = FileStatus.valueOf(statusStr);
        return ResponseEntity.ok(applicationFileServices.updateFileStatus(applicationId, qualificationId, newStatus));
    }

    // RTO information update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/rto-info")
    public ResponseEntity<SuccessResponse> updateRTOInfo(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, Object> body) {
        String rtoCode = (String) body.get("rtoCode");
        double rtoCharge = Double.parseDouble(body.get("rtoCharge").toString());
        RTOPaymentStatus paymentStatus = RTOPaymentStatus.valueOf((String) body.get("paymentStatus"));
        return ResponseEntity.ok(applicationFileServices.updateRTOInfo(applicationId, qualificationId, rtoCode, rtoCharge, paymentStatus));
    }

    // RTO payment date update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/rto-payment-date")
    public ResponseEntity<SuccessResponse> updateRTOPaymentDate(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        LocalDateTime paymentDate = LocalDateTime.parse(body.get("paymentDate"));
        return ResponseEntity.ok(applicationFileServices.updateRTOPaymentDate(applicationId, qualificationId, paymentDate));
    }

    // Lodged to RTO status update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/lodged-status")
    public ResponseEntity<SuccessResponse> updateLodgedToRTO(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, Object> body) {
        boolean lodgedToRTO = (boolean) body.get("lodgedToRTO");
        LocalDateTime lodgedDate = body.get("lodgedDate") != null ?
                LocalDateTime.parse((String) body.get("lodgedDate")) : null;
        return ResponseEntity.ok(applicationFileServices.updateLodgedToRTO(applicationId, qualificationId, lodgedToRTO, lodgedDate));
    }

    // Document received date update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/document-received-date")
    public ResponseEntity<SuccessResponse> updateDocumentReceivedDate(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        LocalDateTime documentReceivedDate = LocalDateTime.parse(body.get("documentReceivedDate"));
        return ResponseEntity.ok(applicationFileServices.updateDocumentReceivedDate(applicationId, qualificationId, documentReceivedDate));
    }

    // Soft copy received date update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/soft-copy-received-date")
    public ResponseEntity<SuccessResponse> updateSoftCopyReceivedDate(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        LocalDateTime softCopyReceivedDate = LocalDateTime.parse(body.get("softCopyReceivedDate"));
        return ResponseEntity.ok(applicationFileServices.updateSoftCopyReceivedDate(applicationId, qualificationId, softCopyReceivedDate));
    }

    // Soft copy released date update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/soft-copy-released-date")
    public ResponseEntity<SuccessResponse> updateSoftCopyReleasedDate(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        LocalDateTime softCopyReleasedDate = LocalDateTime.parse(body.get("softCopyReleasedDate"));
        return ResponseEntity.ok(applicationFileServices.updateSoftCopyReleasedDate(applicationId, qualificationId, softCopyReleasedDate));
    }

    // Hard copy received date update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/hard-copy-received-date")
    public ResponseEntity<SuccessResponse> updateHardCopyReceivedDate(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        LocalDateTime hardCopyReceivedDate = LocalDateTime.parse(body.get("hardCopyReceivedDate"));
        return ResponseEntity.ok(applicationFileServices.updateHardCopyReceivedDate(applicationId, qualificationId, hardCopyReceivedDate));
    }

    // Hard copy mailed date update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/hard-copy-mailed-date")
    public ResponseEntity<SuccessResponse> updateHardCopyMailedDate(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        LocalDateTime hardCopyMailedDate = LocalDateTime.parse(body.get("hardCopyMailedDate"));
        return ResponseEntity.ok(applicationFileServices.updateHardCopyMailedDate(applicationId, qualificationId, hardCopyMailedDate));
    }

    // File details update endpoint
    @PutMapping("/application/{applicationId}/qualification/{qualificationId}/file-details")
    public ResponseEntity<SuccessResponse> updateFileDetails(
            @PathVariable String applicationId,
            @PathVariable String qualificationId,
            @RequestBody Map<String, String> body) {
        String fileSource = body.get("fileSource");
        String visaStatus = body.get("visaStatus");
        String hardCopyTrackingNumber = body.get("hardCopyTrackingNumber");
        return ResponseEntity.ok(applicationFileServices.updateFileDetails(applicationId, qualificationId, fileSource, visaStatus, hardCopyTrackingNumber));
    }

    @GetMapping("/application/{applicationId}/qualification/{qualificationId}/file")
    public ResponseEntity<FileStatusDTO> getSingleApplicationFile(
            @PathVariable String applicationId,
            @PathVariable String qualificationId) {
        return ResponseEntity.ok(applicationFileServices.getSingleApplicationFile(applicationId, qualificationId));
    }

    @GetMapping("/application-files/all")
    public ResponseEntity<List<FileStatusDTO>> getAllApplicationFiles() {
        return ResponseEntity.ok(applicationFileServices.getAllApplicationFiles());
    }

    // Application Comments endpoints
    @PostMapping("/application/{applicationId}/comment")
    public ResponseEntity<ApplicationCommentDTO> addApplicationComment(
            @PathVariable String applicationId,
            @RequestBody Map<String, String> body) {
        String comment = body.get("comment");
        ApplicationCommentRequestDTO commentDTO = new ApplicationCommentRequestDTO(comment, applicationId);
        return ResponseEntity.ok(companyServices.addApplicationComment(commentDTO));
    }

    @GetMapping("/application/{applicationId}/comments")
    public ResponseEntity<List<ApplicationCommentDTO>> getApplicationComments(@PathVariable String applicationId) {
        return ResponseEntity.ok(companyServices.getApplicationComments(applicationId));
    }

    @PutMapping("/application/{applicationId}/comment/{commentId}")
    public ResponseEntity<ApplicationCommentDTO> updateApplicationComment(
            @PathVariable String applicationId,
            @PathVariable Long commentId,
            @RequestBody Map<String, String> body) {
        String newContent = body.get("content");
        return ResponseEntity.ok(companyServices.updateApplicationComment(commentId, applicationId, newContent));
    }

    @DeleteMapping("/application/{applicationId}/comment/{commentId}")
    public ResponseEntity<SuccessResponse> deleteApplicationComment(
            @PathVariable String applicationId,
            @PathVariable Long commentId) {
        return ResponseEntity.ok(companyServices.deleteApplicationComment(commentId, applicationId));
    }

}
