import React, { useState, useEffect, useCallback } from "react";
import {
  useGetAgentLeadsFilteredQuery,
  useUploadLeadsMutation,
  useUploadSingleLeadsMutation,
  useUpdateLeadStatusMutation,
  useAddLeadCommentMutation,
  useUpdateLeadCommentMutation,
  useDeleteLeadCommentMutation,
  useEditLeadMutation,
  useGetProfileQuery,
} from "../../services/CompanyAPIService";
import { useGetAllAgentsQuery } from "../../services/AdminAPIService";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useNavigate } from "react-router-dom";
import { getCurrentUserFromToken } from "../../utils/jwtUtils";

// Components
import LeadPageHeader from "../../components/leads/LeadPageHeader";
import LeadStatsCards from "../../components/leads/LeadStatsCards";
import LeadStatusTabs from "../../components/leads/LeadStatusTabs";
import LeadTableContainer from "../../components/leads/LeadTableContainer";
import LeadFullScreenTable from "../../components/leads/LeadFullScreenTable";
import LeadDrawer from "../../components/leads/LeadDrawer";
import BulkLeadUploadModal from "../../components/modal/BulkLeadUploadModal";
import EditLeadModal from "../../components/modal/EditLeadModal";
import SingleLeadModal from "../../components/modal/SingleLeadModal";

// Hooks
import { useBackendLeadFilters } from "../../hooks/useBackendLeadFilters";
import { useLeadModals } from "../../hooks/useLeadModals";

const AgentLeadsPage = () => {
  // =======================
  // Backend Lead Filters Hook
  // =======================
  const {
    leadSearch,
    setLeadSearch,
    selectedAgentFilter,
    setSelectedAgentFilter,
    leadTypeFilter,
    setLeadTypeFilter,
    leadTab,
    setLeadTab,
    sortField,
    setSortField,
    sortDirection,
    setSortDirection,
    currentPage,
    allLeads,
    hasMore,
    stats,
    agentNames,
    cardTitleSuffix,
    buildFilterCriteria,
    loadMore,
    handleSort,
    handleLeadTabChange,
    updateLeadsData,
    sortedLeads,
  } = useBackendLeadFilters();

  // =======================
  // RTK Query Hooks
  // =======================
  const {
    data: leadsResponse,
    isLoading: isLeadsLoading,
    isFetching: isLeadsFetching,
    error: leadsError,
    refetch: refetchLeads,
  } = useGetAgentLeadsFilteredQuery(buildFilterCriteria(currentPage), {
    skip: false, // Always fetch
  });

  const { data: employees = [] } = useGetAllAgentsQuery();
  const { data: profileData } = useGetProfileQuery();

  const [uploadLeads, { isLoading: isUploading }] = useUploadLeadsMutation();
  const [uploadSingleLeads, { isLoading: isSingleLeadUploading }] = useUploadSingleLeadsMutation();
  const [updateLeadStatus, { isLoading: isUpdatingStatus }] = useUpdateLeadStatusMutation();
  const [addLeadComment, { isLoading: isAddingComment }] = useAddLeadCommentMutation();
  const [updateLeadComment] = useUpdateLeadCommentMutation();
  const [deleteLeadComment] = useDeleteLeadCommentMutation();
  const [editLead, { isLoading: isEditingLead }] = useEditLeadMutation();

  // Get current user info from JWT token (primary) and fallback to profile API
  const tokenUser = getCurrentUserFromToken();
  const currentUser = tokenUser ? {
    username: tokenUser.username,
    fullName: tokenUser.fullName || (profileData ? profileData.fullName : tokenUser.username)
  } : (profileData ? {
    username: profileData.username,
    fullName: profileData.fullName
  } : null);

  const navigate = useNavigate();

  // =======================
  // Handle API Response
  // =======================
  useEffect(() => {
    if (leadsResponse) {
      const isFirstPage = currentPage === 0;
      updateLeadsData(leadsResponse, isFirstPage);
    }
  }, [leadsResponse, currentPage, updateLeadsData]);

  const {
    isModalOpen,
    uploadFile,
    selectedSalesReps,
    salesRepSearch,
    isSalesRepDropdownOpen,
    isSingleLeadModalOpen,
    isEditLeadModalOpen,
    leadToEdit,
    isDrawerOpen,
    selectedLead,
    drawerTab,
    applicantSearch,
    newComment,
    isFullScreen,
    setUploadFile,
    setSelectedSalesReps,
    setSalesRepSearch,
    setIsSalesRepDropdownOpen,
    setDrawerTab,
    setApplicantSearch,
    setNewComment,
    setIsFullScreen,
    setSelectedLeadData,
    openModal,
    closeModal,
    openSingleLeadModal,
    closeSingleLeadModal,
    handleOpenEditModal,
    handleCloseEditModal,
    openDrawer,
    closeDrawer,
    handleFileSelect,
    handleFileDrop,
    handleDragOver,
    handleDragEnter,
    toggleSalesRepSelection,
    handleSalesRepSearchChange
  } = useLeadModals();

  // Update selected lead when leads data changes
  useEffect(() => {
    if (selectedLead) {
      const updatedLead = allLeads.find(lead => lead.phone === selectedLead.phone);
      if (updatedLead) {
        // Update the selected lead with new data
        setSelectedLeadData(updatedLead);
      }
    }
  }, [allLeads, selectedLead, setSelectedLeadData]);

  // =======================
  // API Handlers
  // =======================

  const handleSubmitLeads = async () => {
    if (!uploadFile) {
      toast.error("Please upload a CSV or Excel file.");
      return;
    }
    try {
      const formData = new FormData();
      formData.append("file", uploadFile);
      selectedSalesReps.forEach(username => {
        formData.append("assigned", username);
      });
      await uploadLeads(formData).unwrap();
      toast.success("Leads uploaded successfully!");
      closeModal();
      refetchLeads();
    } catch (error) {
      console.error("Error uploading leads:", error);
      toast.error("There was an error uploading the file.");
    }
  };

  const handleSingleLeadSubmit = async (payload) => {
    try {
      await uploadSingleLeads(payload).unwrap();
      toast.success("Single lead added successfully!");
      closeSingleLeadModal();
      refetchLeads();
      navigate(`/agent/lead/profile/${payload.phone}`);
    } catch (err) {
      const errMessage = err?.data?.message || "There was an error adding the lead.";
      console.error("Error adding single lead:", err);
      toast.error(errMessage);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !selectedLead) return;

    // Store the comment text before clearing it
    const commentText = newComment;

    // Clear the comment field immediately
    setNewComment("");

    try {
      await addLeadComment({ leadPhone: selectedLead.phone, comment: commentText }).unwrap();
      toast.success("Comment added successfully!");
      refetchLeads();
    } catch (error) {
      console.error("Failed to add comment:", error);
      toast.error("There was an error adding the comment.");
    }
  };

  const handleEditComment = async (leadPhone, commentId, content) => {
    try {
      await updateLeadComment({ leadPhone, commentId, content }).unwrap();
      toast.success("Comment updated successfully!");
      refetchLeads();
    } catch (error) {
      console.error("Failed to update comment:", error);
      toast.error("There was an error updating the comment.");
    }
  };

  const handleDeleteComment = async (leadPhone, commentId) => {
    try {
      await deleteLeadComment({ leadPhone, commentId }).unwrap();
      toast.success("Comment deleted successfully!");
      refetchLeads();
    } catch (error) {
      console.error("Failed to delete comment:", error);
      toast.error("There was an error deleting the comment.");
    }
  };

  const filteredApplications = selectedLead?.applications && selectedLead.applications.filter(
    (app) =>
      app.fullName.toLowerCase().includes(applicantSearch.toLowerCase()) ||
      app.applicationId.toLowerCase().includes(applicantSearch.toLowerCase())
  );

  const handleStatusChange = async (leadPhone, newStatus) => {
    try {
      await updateLeadStatus({ leadPhone, status: newStatus }).unwrap();
      toast.success(`Lead status updated to ${newStatus}!`);
      refetchLeads();
    } catch (error) {
      console.error("Failed to update lead status:", error);
      toast.error("There was an error updating the lead status.");
    }
  };

  const handleProfileRedirect = (phone) => navigate(`/agent/lead/profile/${phone}`);

  const handleApplicationProfileRedirect = (applicationId) => navigate(`/agent/application/profile/${applicationId}`);

  return (
    <div className="w-full py-6">
      <ToastContainer />

      {/* Page Header */}
      <LeadPageHeader
        onOpenBulkModal={openModal}
        onOpenSingleModal={openSingleLeadModal}
        isAdmin={false}
      />

      {/* Stats Section */}
      <LeadStatsCards
        stats={stats}
        cardTitleSuffix={cardTitleSuffix}
      />

      {/* Status Tabs */}
      <LeadStatusTabs
        leadTab={leadTab}
        onTabChange={handleLeadTabChange}
        totalLeads={stats.totalLeads}
        leadsWithApplications={stats.leadsWithApplications}
        statusCounts={stats.statusCounts}
      />

      {/* Main Table Container */}
      <LeadTableContainer
        leads={sortedLeads}
        onProfileRedirect={handleProfileRedirect}
        onStatusChange={handleStatusChange}
        onEditLead={handleOpenEditModal}
        onOpenDrawer={openDrawer}
        onDeleteLead={null} // Agents can't delete leads
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        leadSearch={leadSearch}
        setLeadSearch={setLeadSearch}
        selectedAgentFilter={selectedAgentFilter}
        setSelectedAgentFilter={setSelectedAgentFilter}
        leadTypeFilter={leadTypeFilter}
        setLeadTypeFilter={setLeadTypeFilter}
        agentNames={agentNames}
        isFullScreen={isFullScreen}
        setIsFullScreen={setIsFullScreen}
        isAdmin={false}
        isLoading={isLeadsLoading || isLeadsFetching}
        isUpdatingStatus={isUpdatingStatus}
        error={leadsError}
        hasMore={hasMore}
        onLoadMore={loadMore}
        isLoadingMore={isLeadsFetching && currentPage > 0}
      />

      {/* Full Screen Table */}
      <LeadFullScreenTable
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        leads={sortedLeads}
        onProfileRedirect={handleProfileRedirect}
        onStatusChange={handleStatusChange}
        onEditLead={handleOpenEditModal}
        onOpenDrawer={openDrawer}
        onDeleteLead={null} // Agents can't delete leads
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        leadSearch={leadSearch}
        setLeadSearch={setLeadSearch}
        selectedAgentFilter={selectedAgentFilter}
        setSelectedAgentFilter={setSelectedAgentFilter}
        leadTypeFilter={leadTypeFilter}
        setLeadTypeFilter={setLeadTypeFilter}
        agentNames={agentNames}
        isAdmin={false}
        isLoading={isLeadsLoading || isLeadsFetching}
        isUpdatingStatus={isUpdatingStatus}
        error={leadsError}
        // New props for tabs and header
        leadTab={leadTab}
        onTabChange={handleLeadTabChange}
        totalLeads={stats.totalLeads}
        leadsWithApplications={stats.leadsWithApplications}
        statusCounts={stats.statusCounts}
        onOpenBulkModal={openModal}
        onOpenSingleModal={openSingleLeadModal}
      />

      {/* Bulk Lead Upload Modal */}
      <BulkLeadUploadModal
        isOpen={isModalOpen}
        onClose={closeModal}
        uploadFile={uploadFile}
        selectedSalesReps={selectedSalesReps}
        salesRepSearch={salesRepSearch}
        isSalesRepDropdownOpen={isSalesRepDropdownOpen}
        setIsSalesRepDropdownOpen={setIsSalesRepDropdownOpen}
        employees={employees}
        onFileSelect={handleFileSelect}
        onFileDrop={handleFileDrop}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onSalesRepSearchChange={handleSalesRepSearchChange}
        onToggleSalesRepSelection={toggleSalesRepSelection}
        onSubmit={handleSubmitLeads}
        isUploading={isUploading}
      />

      {/* Single Lead Modal */}
      <SingleLeadModal
        isOpen={isSingleLeadModalOpen}
        onClose={closeSingleLeadModal}
        onSubmit={handleSingleLeadSubmit}
        salesReps={employees.map(emp => ({ username: emp.username, name: emp.fullName }))}
        currentUser={currentUser}
        isAgent={true}
      />

      {/* Edit Lead Modal */}
      <EditLeadModal
        isOpen={isEditLeadModalOpen}
        onClose={handleCloseEditModal}
        lead={leadToEdit}
      />

      {/* Lead Drawer */}
      <LeadDrawer
        isOpen={isDrawerOpen}
        onClose={closeDrawer}
        lead={selectedLead}
        drawerTab={drawerTab}
        setDrawerTab={setDrawerTab}
        applicantSearch={applicantSearch}
        setApplicantSearch={setApplicantSearch}
        newComment={newComment}
        setNewComment={setNewComment}
        onAddComment={handleAddComment}
        onEditComment={handleEditComment}
        onDeleteComment={handleDeleteComment}
        filteredApplications={filteredApplications}
        onProfileRedirect={handleApplicationProfileRedirect}
        isAddingComment={isAddingComment}
      />
    </div>
  );
};

export default AgentLeadsPage;
