import { createApi } from "@reduxjs/toolkit/query/react";
import { createCustomBaseQuery } from "./customBaseQuery";

export const CompanyAPIService = createApi({
  reducerPath: "CompanyAPIService",
  baseQuery: createCustomBaseQuery("/api/generic"),
  // Added QuoteRequests and RTOs as new tags for cache management
  tagTypes: ["Applications", "TimeTracker", "Targets", "Leads", "QuoteRequests", "RTOs", "Commissions", "FileStatus"],
  endpoints: (builder) => ({
    // ----- Time & Profile Endpoints -----
    updateTimeStamp: builder.mutation({
      query: () => ({
        url: "/time/update",
        method: "GET",
      }),
      invalidatesTags: [{ type: "TimeTracker", id: "TODAY" }],
    }),
    getTodayTimeTracker: builder.query({
      query: () => "/tracker/today",
      providesTags: [{ type: "TimeTracker", id: "TODAY" }],
    }),
    getTrackersByMonthYear: builder.query({
      query: ({ username, month, year }) => `/tracker/${username}/${month}/${year}`,
      providesTags: (result, error, { username }) => [{ type: "TimeTracker", id: username }],
    }),
    getProfile: builder.query({ query: () => "/profile" }),
    getAgentProfile: builder.query({ query: () => "/agent/profile" }),

    // ----- Leads Endpoints -----
    getLeads: builder.query({
      query: () => "/leads",
      providesTags: [{ type: "Leads", id: "LIST" }],
    }),
    getLeadsPaginated: builder.query({
      query: (criteria) => ({
        url: "/leads/paginated",
        method: "POST",
        body: criteria,
      }),
      providesTags: [{ type: "Leads", id: "PAGINATED" }],
    }),
    getAgentLeads: builder.query({
      query: () => "/agent/leads",
      providesTags: [{ type: "Leads", id: "LIST" }],
    }),
    getAgentLeadsFiltered: builder.query({
      query: (criteria) => ({
        url: "/agent/leads/filtered",
        method: "POST",
        body: criteria,
      }),
      providesTags: [{ type: "Leads", id: "AGENT_FILTERED" }],
    }),
    getLeadStats: builder.query({
      query: (criteria) => ({
        url: "/leads/stats",
        method: "POST",
        body: criteria,
      }),
      providesTags: [{ type: "Leads", id: "STATS" }],
    }),
    getAgentLeadStats: builder.query({
      query: (criteria) => ({
        url: "/agent/leads/stats",
        method: "POST",
        body: criteria,
      }),
      providesTags: [{ type: "Leads", id: "AGENT_STATS" }],
    }),
    uploadLeads: builder.mutation({
      query: (formData) => ({
        url: "/leads/upload",
        method: "POST",
        body: formData,
      }),
      invalidatesTags: [{ type: "Leads", id: "LIST" }],
    }),
    uploadSingleLeads: builder.mutation({
      query: (formData) => ({
        url: "/leads/single",
        method: "POST",
        body: formData,
      }),
      invalidatesTags: [{ type: "Leads", id: "LIST" }],
    }),
    getLeadByPhone: builder.query({
      query: (phoneNumber) => `/lead/${phoneNumber}`,
      providesTags: (result, error, phoneNumber) => [{ type: "Leads", id: phoneNumber }],
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    updateLeadStatus: builder.mutation({
      query: ({ leadPhone, status }) => ({
        url: `/lead/${leadPhone}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: [
        { type: "Applications", id: "LIST" },
        { type: "Leads", id: "LIST" },
      ],
    }),
    addLeadComment: builder.mutation({
      query: ({ leadPhone, comment }) => ({
        url: `/lead/${leadPhone}/comment`,
        method: "PUT",
        body: { comment },
      }),
      invalidatesTags: [
        { type: "Applications", id: "LIST" },
        { type: "Leads", id: "LIST" },
      ],
    }),
    updateLeadComment: builder.mutation({
      query: ({ leadPhone, commentId, content }) => ({
        url: `/lead/${leadPhone}/comment/${commentId}`,
        method: "PUT",
        body: { content },
      }),
      invalidatesTags: [
        { type: "Applications", id: "LIST" },
        { type: "Leads", id: "LIST" },
      ],
    }),
    deleteLeadComment: builder.mutation({
      query: ({ leadPhone, commentId }) => ({
        url: `/lead/${leadPhone}/comment/${commentId}`,
        method: "DELETE",
      }),
      invalidatesTags: [
        { type: "Applications", id: "LIST" },
        { type: "Leads", id: "LIST" },
      ],
    }),
    changeLeadOwner: builder.mutation({
      query: ({ leadPhone, newOwner }) => ({
        url: `/lead/${leadPhone}/owner`,
        method: "PUT",
        body: { newOwner },
      }),
      invalidatesTags: [{ type: "Leads", id: "LIST" }],
    }),
    editLead: builder.mutation({
      query: (leadData) => ({
        url: "/lead/edit",
        method: "PUT",
        body: leadData,
      }),
      invalidatesTags: [{ type: "Leads", id: "LIST" }],
    }),
    deleteLead: builder.mutation({
      query: (phone) => ({
        url: `/lead/${phone}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Leads", id: "LIST" }],
    }),
    exportLeads: builder.mutation({
      query: (criteria) => ({
        url: "/leads/export",
        method: "POST",
        body: criteria,
        responseHandler: async (response) => {
          const blob = await response.blob();
          return blob;
        },
      }),
    }),
    exportLeadsEnhanced: builder.mutation({
      query: (exportRequest) => ({
        url: "/leads/export-enhanced",
        method: "POST",
        body: exportRequest,
        responseHandler: async (response) => {
          const blob = await response.blob();
          return blob;
        },
      }),
    }),
    exportLeadsLegacy: builder.mutation({
      query: ({ search, agentFilter, typeFilter, selectedLeads }) => {
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (agentFilter) params.append('agentFilter', agentFilter);
        if (typeFilter && typeFilter !== 'all') params.append('typeFilter', typeFilter);
        if (selectedLeads && selectedLeads.length > 0) {
          selectedLeads.forEach(phone => params.append('selectedLeads', phone));
        }

        return {
          url: `/leads/export?${params.toString()}`,
          method: "GET",
          responseHandler: async (response) => {
            const blob = await response.blob();
            return blob;
          },
        };
      },
    }),

    // ----- Applications Endpoints -----
    getAllApplications: builder.query({
      query: () => "/applications/all",
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ applicationId }) => ({ type: "Applications", id: applicationId })),
              { type: "Applications", id: "LIST" },
            ]
          : [{ type: "Applications", id: "LIST" }],
    }),
    getAllApplicationsOfAgent: builder.query({
      query: () => "/applications",
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ applicationId }) => ({ type: "Applications", id: applicationId })),
              { type: "Applications", id: "LIST" },
            ]
          : [{ type: "Applications", id: "LIST" }],
    }),
    getSingleApplication: builder.query({
      query: (applicationId) => `/application/${applicationId}`,
      providesTags: (result, error, applicationId) => [{ type: "Applications", id: applicationId }],
    }),
    createApplication: builder.mutation({
      query: (applicationData) => ({
        url: "/applications",
        method: "POST",
        body: applicationData,
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    changeApplicationStatus: builder.mutation({
      query: ({ applicationId, status }) => ({
        url: `/applications/${applicationId}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    updateApplicationQuote: builder.mutation({
      query: ({ applicationId, quote }) => ({
        url: `/applications/${applicationId}/quote`,
        method: "PUT",
        body: { quote },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    updateApplicationInvoice: builder.mutation({
      query: ({ applicationId, invoice }) => ({
        url: `/applications/${applicationId}/invoice`,
        method: "PUT",
        body: { invoice },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    updateApplicantInfo: builder.mutation({
      query: ({ applicationId, applicantName, applicantPhone, applicantEmail, applicantAddress }) => ({
        url: `/applications/${applicationId}/info`,
        method: "PUT",
        body: { applicantName, applicantPhone, applicantEmail, applicantAddress },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    addQualification: builder.mutation({
      query: ({ applicationId, qualificationId, qualificationName, price }) => ({
        url: `/applications/${applicationId}/qualification`,
        method: "PUT",
        body: { qualificationId, qualificationName, price },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    removeQualification: builder.mutation({
      query: ({ applicationId, qualificationId }) => ({
        url: `/applications/${applicationId}/${qualificationId}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    deleteApplication: builder.mutation({
      query: (applicationId) => ({
        url: `/applications/${applicationId}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),

    // ----- Payment Endpoints -----
    updateApplicationPaymentStatus: builder.mutation({
      query: ({ applicationId, status }) => ({
        url: `/payment/${applicationId}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    updateApplicationPaidAmount: builder.mutation({
      query: ({ applicationId, paid }) => ({
        url: `/payment/${applicationId}/partial`,
        method: "PUT",
        body: { paid },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    updateApplicationCreatedDate: builder.mutation({
      query: ({ applicationId, createdDate }) => ({
        url: `/applications/${applicationId}/created-date`,
        method: "PUT",
        body: { createdDate },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
    getPaymentDetails: builder.query({
      query: (applicationId) => `/payment/${applicationId}`,
      providesTags: (result, error, applicationId) => [{ type: "Applications", id: applicationId }],
    }),

    // ----- Dashboard, Password, and Targets Endpoints -----
    getAgentDashboard: builder.query({
      query: ({ startDate, endDate, targetId, targetIds }) => {
        if (targetIds && targetIds.length > 0) {
          const targetIdsParam = targetIds.map(id => `targetIds=${id}`).join('&');
          return `/agent/dashboard/${startDate}/${endDate}?${targetIdsParam}`;
        }
        if (targetId) {
          return `/agent/dashboard/${startDate}/${endDate}?targetId=${targetId}`;
        }
        return `/agent/dashboard/${startDate}/${endDate}`;
      },
    }),

    getAgentDashboardThisMonth: builder.query({
      query: () => `/agent/dashboard/this-month`,
    }),

    getAgentDashboardThisYear: builder.query({
      query: () => `/agent/dashboard/this-year`,
    }),
    changePassword: builder.mutation({
      query: ({ oldPassword, newPassword }) => ({
        url: "/update/password",
        method: "PUT",
        body: { oldPassword, newPassword },
      }),
    }),
    getAllTargets: builder.query({
      query: () => "/target/all",
      providesTags: (result) =>
        result
          ? [
              ...result.map((t) => ({ type: "Targets", id: t.title })),
              { type: "Targets", id: "LIST" },
            ]
          : [{ type: "Targets", id: "LIST" }],
    }),
    createTarget: builder.mutation({
      query: (targetData) => ({
        url: "/target/create",
        method: "POST",
        body: targetData,
      }),
      invalidatesTags: [{ type: "Targets", id: "LIST" }],
    }),
    updateTarget: builder.mutation({
      query: (targetData) => ({
        url: "/target/update",
        method: "PUT",
        body: targetData,
      }),
      invalidatesTags: [{ type: "Targets", id: "LIST" }],
    }),

    // ----- Quote & Invoice Request Endpoints -----
    getAllQuoteRequests: builder.query({
      query: () => "/quote/request/all",
      providesTags: [{ type: "QuoteRequests", id: "LIST" }],
      // Force a complete reset and fresh fetch
      keepUnusedDataFor: 0, // Don't keep data in cache
    }),
    getDraftedQuoteRequests: builder.query({
      query: () => "/quote/request/drafted",
      providesTags: [{ type: "QuoteRequests", id: "DRAFTED" }],
      // Force a complete reset and fresh fetch
      keepUnusedDataFor: 0, // Don't keep data in cache
    }),
    getRequestedQuoteRequests: builder.query({
      query: () => "/quote/request/requests",
      providesTags: [{ type: "QuoteRequests", id: "REQUESTS" }],
      // Force a complete reset and fresh fetch
      keepUnusedDataFor: 0, // Don't keep data in cache
    }),
    changeQuoteStatus: builder.mutation({
      query: (requestDTO) => ({
        url: "/quote/request/status/edit",
        method: "PUT",
        body: requestDTO,
      }),
      invalidatesTags: [{ type: "QuoteRequests", id: "LIST" }],
    }),
    changeInvoiceStatus: builder.mutation({
      query: (requestDTO) => ({
        url: "/invoice/request/status/edit",
        method: "PUT",
        body: requestDTO,
      }),
      invalidatesTags: [{ type: "QuoteRequests", id: "LIST" }],
    }),
    raiseQuoteAndInvoice: builder.mutation({
      query: (requestDTO) => ({
        url: "/application/raise/initial",
        method: "PUT",
        body: requestDTO,
      }),
      invalidatesTags: [{ type: "QuoteRequests", id: "LIST" }],
    }),

    // ----- External Commission Endpoints -----
    getAllCommissions: builder.query({
      query: () => "/commissions/all",
      providesTags: [{ type: "Commissions", id: "LIST" }],
    }),
    updateCommissionStatus: builder.mutation({
      query: ({ applicationId, status }) => ({
        url: `/commission/${applicationId}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: [{ type: "Commissions", id: "LIST" }],
    }),
    editCommission: builder.mutation({
      query: ({ applicationId, commissionDTO }) => ({
        url: `/commission/${applicationId}/edit`,
        method: "PUT",
        body: commissionDTO,
      }),
      invalidatesTags: [{ type: "Commissions", id: "LIST" }],
    }),
    deleteCommission: builder.mutation({
      query: (applicationId) => ({
        url: `/commission/${applicationId}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Commissions", id: "LIST" }],
    }),
    createCommission: builder.mutation({
      query: (commissionDTO) => ({
        url: "/commission/create",
        method: "POST",
        body: commissionDTO,
      }),
      invalidatesTags: [{ type: "Commissions", id: "LIST" }],
    }),

    // ----- File Status Endpoints -----
    getAllApplicationFiles: builder.query({
      query: () => "/application-files/all",
      providesTags: [{ type: "FileStatus", id: "LIST" }],
    }),
    getSingleApplicationFile: builder.query({
      query: ({ applicationId, qualificationId }) => `/application/${applicationId}/qualification/${qualificationId}/file`,
      providesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` }
      ],
    }),
    updateFileStatus: builder.mutation({
      query: ({ applicationId, qualificationId, status }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/file-status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateRTOInfo: builder.mutation({
      query: ({ applicationId, qualificationId, rtoCode, rtoCharge, paymentStatus }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/rto-info`,
        method: "PUT",
        body: { rtoCode, rtoCharge, paymentStatus },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateRTOPaymentDate: builder.mutation({
      query: ({ applicationId, qualificationId, paymentDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/rto-payment-date`,
        method: "PUT",
        body: { paymentDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateLodgedToRTO: builder.mutation({
      query: ({ applicationId, qualificationId, lodgedToRTO, lodgedDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/lodged-status`,
        method: "PUT",
        body: { lodgedToRTO, lodgedDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateDocumentReceivedDate: builder.mutation({
      query: ({ applicationId, qualificationId, documentReceivedDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/document-received-date`,
        method: "PUT",
        body: { documentReceivedDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateSoftCopyReceivedDate: builder.mutation({
      query: ({ applicationId, qualificationId, softCopyReceivedDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/soft-copy-received-date`,
        method: "PUT",
        body: { softCopyReceivedDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateSoftCopyReleasedDate: builder.mutation({
      query: ({ applicationId, qualificationId, softCopyReleasedDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/soft-copy-released-date`,
        method: "PUT",
        body: { softCopyReleasedDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateHardCopyReceivedDate: builder.mutation({
      query: ({ applicationId, qualificationId, hardCopyReceivedDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/hard-copy-received-date`,
        method: "PUT",
        body: { hardCopyReceivedDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateHardCopyMailedDate: builder.mutation({
      query: ({ applicationId, qualificationId, hardCopyMailedDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/hard-copy-mailed-date`,
        method: "PUT",
        body: { hardCopyMailedDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateFileDetails: builder.mutation({
      query: ({ applicationId, qualificationId, fileSource, visaStatus, hardCopyTrackingNumber }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/file-details`,
        method: "PUT",
        body: { fileSource, visaStatus, hardCopyTrackingNumber },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    // Commission info for file status
    updateCommissionInfo: builder.mutation({
      query: ({ applicationId, qualificationId, externalCommissionName, externalCommissionContactInfo, commissionAmount }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/commission-info`,
        method: "PUT",
        body: { externalCommissionName, externalCommissionContactInfo, commissionAmount },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateCommissionPaymentStatus: builder.mutation({
      query: ({ applicationId, qualificationId, paymentStatus }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/commission-payment-status`,
        method: "PUT",
        body: { paymentStatus },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),
    updateCommissionPaymentDate: builder.mutation({
      query: ({ applicationId, qualificationId, paymentDate }) => ({
        url: `/application/${applicationId}/qualification/${qualificationId}/commission-payment-date`,
        method: "PUT",
        body: { paymentDate },
      }),
      invalidatesTags: (result, error, { applicationId, qualificationId }) => [
        { type: "FileStatus", id: `${applicationId}-${qualificationId}` },
        { type: "FileStatus", id: "LIST" }
      ],
    }),

    // ----- RTO Management Endpoints -----
    getAllRTOs: builder.query({
      query: () => "/rto/all",
      providesTags: [{ type: "RTOs", id: "LIST" }],
    }),
    getRTOByCode: builder.query({
      query: (rtoCode) => `/rto/${rtoCode}`,
      providesTags: (result, error, rtoCode) => [{ type: "RTOs", id: rtoCode }],
    }),
    fetchAndSaveRTO: builder.mutation({
      query: (rtoCode) => ({
        url: `/rto/fetch/${rtoCode}`,
        method: "POST",
      }),
      invalidatesTags: [{ type: "RTOs", id: "LIST" }],
    }),
    reloadRTOInfo: builder.mutation({
      query: (rtoCode) => ({
        url: `/rto/reload/${rtoCode}`,
        method: "POST",
      }),
      invalidatesTags: (result, error, rtoCode) => [
        { type: "RTOs", id: "LIST" },
        { type: "RTOs", id: rtoCode },
      ],
    }),
    updateRTOContact: builder.mutation({
      query: ({ contactId, contactDTO }) => ({
        url: `/rto/contact/${contactId}`,
        method: "PUT",
        body: contactDTO,
      }),
      invalidatesTags: [{ type: "RTOs", id: "LIST" }],
    }),
    addRTOContact: builder.mutation({
      query: ({ rtoCode, contactDTO }) => ({
        url: `/rto/${rtoCode}/contact`,
        method: "POST",
        body: contactDTO,
      }),
      invalidatesTags: (result, error, { rtoCode }) => [
        { type: "RTOs", id: "LIST" },
        { type: "RTOs", id: rtoCode },
      ],
    }),
    deleteRTO: builder.mutation({
      query: (rtoCode) => ({
        url: `/rto/${rtoCode}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "RTOs", id: "LIST" }],
    }),
    deleteRTOContact: builder.mutation({
      query: (contactId) => ({
        url: `/rto/contact/${contactId}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "RTOs", id: "LIST" }],
    }),



    // ----- Application Comments Endpoints -----
    addApplicationComment: builder.mutation({
      query: ({ applicationId, comment }) => ({
        url: `/application/${applicationId}/comment`,
        method: "POST",
        body: { comment },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),

    getApplicationComments: builder.query({
      query: (applicationId) => `/application/${applicationId}/comments`,
      providesTags: (result, error, applicationId) => [{ type: "Applications", id: applicationId }],
    }),

    updateApplicationComment: builder.mutation({
      query: ({ applicationId, commentId, content }) => ({
        url: `/application/${applicationId}/comment/${commentId}`,
        method: "PUT",
        body: { content },
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),

    deleteApplicationComment: builder.mutation({
      query: ({ applicationId, commentId }) => ({
        url: `/application/${applicationId}/comment/${commentId}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Applications", id: "LIST" }],
    }),
  }),
});

export const {
  useUpdateTimeStampMutation,
  useGetTodayTimeTrackerQuery,
  useGetTrackersByMonthYearQuery,
  useGetProfileQuery,
  useGetAgentProfileQuery,
  useGetLeadsQuery,
  useGetLeadsPaginatedQuery,
  useGetAgentLeadsQuery,
  useGetAgentLeadsFilteredQuery,
  useGetLeadStatsQuery,
  useGetAgentLeadStatsQuery,
  useUploadLeadsMutation,
  useUploadSingleLeadsMutation,
  useGetLeadByPhoneQuery,
  useUpdateLeadStatusMutation,
  useAddLeadCommentMutation,
  useUpdateLeadCommentMutation,
  useDeleteLeadCommentMutation,
  useChangeLeadOwnerMutation,
  useEditLeadMutation,
  useDeleteLeadMutation,
  useExportLeadsMutation,
  useExportLeadsEnhancedMutation,
  useExportLeadsLegacyMutation,
  useGetAllApplicationsQuery,
  useGetAllApplicationsOfAgentQuery,
  useGetSingleApplicationQuery,
  useCreateApplicationMutation,
  useChangeApplicationStatusMutation,
  useUpdateApplicationQuoteMutation,
  useUpdateApplicationInvoiceMutation,
  useUpdateApplicantInfoMutation,
  useAddQualificationMutation,
  useRemoveQualificationMutation,
  useDeleteApplicationMutation,
  useUpdateApplicationPaymentStatusMutation,
  useUpdateApplicationPaidAmountMutation,
  useUpdateApplicationCreatedDateMutation,
  useGetPaymentDetailsQuery,
  useGetAgentDashboardQuery,
  useGetAgentDashboardThisMonthQuery,
  useGetAgentDashboardThisYearQuery,
  useChangePasswordMutation,
  useGetAllTargetsQuery,
  useCreateTargetMutation,
  useUpdateTargetMutation,
  useGetAllQuoteRequestsQuery,
  useGetDraftedQuoteRequestsQuery,
  useGetRequestedQuoteRequestsQuery,
  useChangeQuoteStatusMutation,
  useChangeInvoiceStatusMutation,
  useRaiseQuoteAndInvoiceMutation,
  // RTO Management hooks
  useGetAllRTOsQuery,
  useGetRTOByCodeQuery,
  useFetchAndSaveRTOMutation,
  useReloadRTOInfoMutation,
  useUpdateRTOContactMutation,
  useAddRTOContactMutation,
  useDeleteRTOMutation,
  useDeleteRTOContactMutation,

  // External Commission hooks
  useGetAllCommissionsQuery,
  useUpdateCommissionStatusMutation,
  useEditCommissionMutation,
  useDeleteCommissionMutation,
  useCreateCommissionMutation,
  // File Status hooks
  useGetAllApplicationFilesQuery,
  useGetSingleApplicationFileQuery,
  useUpdateFileStatusMutation,
  useUpdateRTOInfoMutation,
  useUpdateRTOPaymentDateMutation,
  useUpdateLodgedToRTOMutation,
  useUpdateDocumentReceivedDateMutation,
  useUpdateSoftCopyReceivedDateMutation,
  useUpdateSoftCopyReleasedDateMutation,
  useUpdateHardCopyReceivedDateMutation,
  useUpdateHardCopyMailedDateMutation,
  useUpdateFileDetailsMutation,
  useUpdateCommissionInfoMutation,
  useUpdateCommissionPaymentStatusMutation,
  useUpdateCommissionPaymentDateMutation,
  // Application Comments hooks
  useAddApplicationCommentMutation,
  useGetApplicationCommentsQuery,
  useUpdateApplicationCommentMutation,
  useDeleteApplicationCommentMutation,
} = CompanyAPIService;

export default CompanyAPIService;
