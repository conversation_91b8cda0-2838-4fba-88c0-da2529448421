import { useState, useMemo, useCallback, useEffect } from 'react';
import {
  useGetLeadStatsQuery,
  useGetAgentLeadStatsQuery
} from "../services/CompanyAPIService";

export const useBackendLeadFilters = (isAdmin = true) => {
  // Filter states
  const [leadSearch, setLeadSearch] = useState("");
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [leadTypeFilter, setLeadTypeFilter] = useState("all");
  const [leadTab, setLeadTab] = useState("All");
  
  // Sorting states
  const [sortField, setSortField] = useState(null);
  const [sortDirection, setSortDirection] = useState('desc'); // Default to desc for newest first
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(20); // Fixed page size for infinite scroll
  const [allLeads, setAllLeads] = useState([]); // Accumulated leads for infinite scroll
  const [hasMore, setHasMore] = useState(true);

  // Build filter criteria for backend
  const buildFilterCriteria = useCallback((page = 0, resetData = false) => {
    const criteria = {
      page,
      size: pageSize,
      sortField: sortField || 'createdDate',
      sortDirection,
      search: leadSearch || null,
      agentFilter: selectedAgentFilter || null,
      leadTypeFilter: leadTypeFilter === "all" ? null : leadTypeFilter,
    };

    // Map lead tab to appropriate filters
    if (leadTab === "All") {
      // No additional filters
    } else if (leadTab === "Leads with Applications") {
      criteria.applicationFilter = "hasApplications";
    } else if (leadTab === "COLD_FRESH") {
      // This will need special handling - we'll use statusFilter with multiple values
      // For now, we'll handle this in the backend by checking for COLD or FRESH
      criteria.statusFilter = "COLD_FRESH";
    } else {
      // Direct status mapping for HOT, WARM, CLOSED
      criteria.statusFilter = leadTab;
    }

    return criteria;
  }, [leadSearch, selectedAgentFilter, leadTypeFilter, leadTab, sortField, sortDirection, pageSize]);

  // Reset pagination when filters change
  const resetPagination = useCallback(() => {
    setCurrentPage(0);
    setAllLeads([]);
    setHasMore(true);
  }, []);

  // Effect to reset pagination when filters change
  useEffect(() => {
    resetPagination();
  }, [leadSearch, selectedAgentFilter, leadTypeFilter, leadTab, sortField, sortDirection, resetPagination]);

  // Load more data for infinite scroll
  const loadMore = useCallback(() => {
    if (hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasMore]);

  // Sorting handlers
  const handleSort = useCallback((field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);

  const handleLeadTabChange = useCallback((tab) => {
    setLeadTab(tab);
  }, []);

  // Helper to update leads data from API response
  const updateLeadsData = useCallback((response, isFirstPage = false) => {
    if (isFirstPage) {
      setAllLeads(response.leads || []);
    } else {
      setAllLeads(prev => [...prev, ...(response.leads || [])]);
    }
    setHasMore(response.hasNext || false);
  }, []);

  // Calculate stats from current leads data
  const stats = useMemo(() => {
    const totalLeads = allLeads.length;
    const statusCounts = allLeads.reduce((acc, lead) => {
      const status = lead.status || 'UNKNOWN';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const leadsWithApplications = allLeads.filter(lead => lead.applicationCount > 0).length;

    return {
      totalLeads,
      leadsWithApplications,
      statusCounts: {
        HOT: statusCounts.HOT || 0,
        WARM: statusCounts.WARM || 0,
        COLD: statusCounts.COLD || 0,
        FRESH: statusCounts.FRESH || 0,
        CLOSED: statusCounts.CLOSED || 0,
      }
    };
  }, [allLeads]);

  // Get unique agent names from current leads data
  const agentNames = useMemo(() => {
    const names = new Set();
    allLeads.forEach(lead => {
      if (lead.assignedAgents) {
        lead.assignedAgents.forEach(agent => {
          names.add(agent.fullName);
        });
      }
    });
    return Array.from(names).sort();
  }, [allLeads]);

  // Filter text for display
  const agentFilterText = selectedAgentFilter ? selectedAgentFilter : "All";
  const leadTypeFilterText = leadTypeFilter === "all" ? "" : ` - ${leadTypeFilter}`;
  const cardTitleSuffix = selectedAgentFilter || leadTypeFilter !== "all" ? `(${agentFilterText}${leadTypeFilterText})` : "";

  return {
    // Filter states
    leadSearch,
    setLeadSearch,
    selectedAgentFilter,
    setSelectedAgentFilter,
    leadTypeFilter,
    setLeadTypeFilter,
    leadTab,
    setLeadTab,
    
    // Sorting states
    sortField,
    setSortField,
    sortDirection,
    setSortDirection,
    
    // Pagination states
    currentPage,
    pageSize,
    allLeads,
    hasMore,
    
    // Computed data
    stats,
    agentNames,
    cardTitleSuffix,
    
    // Methods
    buildFilterCriteria,
    resetPagination,
    loadMore,
    handleSort,
    handleLeadTabChange,
    updateLeadsData,
    
    // For backward compatibility with existing components
    sortedLeads: allLeads, // The leads are already sorted by backend
    filteredLeads: allLeads, // The leads are already filtered by backend
    filteredLeadsForStats: allLeads, // Same data for stats
  };
};
