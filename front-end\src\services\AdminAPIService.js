import { createApi } from "@reduxjs/toolkit/query/react";
import { createCustomBaseQuery } from "./customBaseQuery";

export const AdminAPIService = createApi({
  reducerPath: "AdminAPIService",
  baseQuery: createCustomBaseQuery("/api/company"),
  tagTypes: ["Admin", "XeroData", "Qualifications", "RTOs"],
  endpoints: (builder) => ({
    getCompanyDashboard: builder.query({
      query: ({ startDate, endDate, targetId, targetIds }) => {
        if (targetIds && targetIds.length > 0) {
          const targetIdsParam = targetIds.map(id => `targetIds=${id}`).join('&');
          return `/dashboard/${startDate}/${endDate}?${targetIdsParam}`;
        }
        if (targetId) {
          return `/dashboard/${startDate}/${endDate}?targetId=${targetId}`;
        }
        return `/dashboard/${startDate}/${endDate}`;
      },
    }),

    getCompanyDashboardThisMonth: builder.query({
      query: () => `/dashboard/this-month`,
    }),

    getCompanyDashboardThisYear: builder.query({
      query: () => `/dashboard/this-year`,
    }),

    getAllEmployee: builder.query({
      query: () => "/employee/all",
    }),

    getAllAgents: builder.query({
      query: () => "/employee/agents/all",
    }),

    getAllApplication: builder.query({
      query: () => "/applications",
    }),

    updateWeeklyTarget: builder.mutation({
      query: ({ username, amount }) => ({
        url: `/target/${username}/${amount}`,
        method: "PUT",
      }),
    }),

    addQualification: builder.mutation({
      query: (qualificationData) => ({
        url: "/qualification/add",
        method: "POST",
        body: qualificationData,
      }),
      invalidatesTags: ["Admin"],
    }),

    editQualification: builder.mutation({
      query: (qualificationData) => ({
        url: "/qualification/edit",
        method: "PUT",
        body: qualificationData,
      }),
      invalidatesTags: ["Admin"],
    }),

    registerAdmin: builder.mutation({
      query: (adminInfo) => ({
        url: "/admin/add",
        method: "POST",
        body: adminInfo,
      }),
      invalidatesTags: ["Admin"],
    }),

    registerSales: builder.mutation({
      query: (salesInfo) => ({
        url: "/sales/add",
        method: "POST",
        body: salesInfo,
      }),
      invalidatesTags: ["Admin"],
    }),

    registerOperations: builder.mutation({
      query: (operationsInfo) => ({
        url: "/operations/add",
        method: "POST",
        body: operationsInfo,
      }),
      invalidatesTags: ["Admin"],
    }),

    deleteEmployee: builder.mutation({
      query: (username) => ({
        url: `/employee/${username}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Admin"],
    }),

    // Xero Integration endpoints
    uploadXeroInvoice: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        return {
          url: '/xero/import/kpi1',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ["XeroData"],
    }),

    getAllKPI1XeroData: builder.query({
      query: () => '/xero/kpi1',
      providesTags: ["XeroData"],
    }),

    // KPI2 Xero Integration endpoints
    uploadXeroInBank: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        return {
          url: '/xero/import/kpi2',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ["XeroData"],
    }),

    getAllKPI2XeroData: builder.query({
      query: () => '/xero/kpi2',
      providesTags: ["XeroData"],
    }),

    // Bulk import qualifications
    bulkImportQualifications: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        return {
          url: '/qualification/bulk-import',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ["Admin"],
    }),

    // Delete operations
    deleteQualification: builder.mutation({
      query: (qualificationId) => ({
        url: `/qualification/${qualificationId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Admin"],
    }),

    // RTO-Qualification Management endpoints
    addRTOToQualification: builder.mutation({
      query: ({ qualificationId, rtoCode }) => ({
        url: `/qualification/${qualificationId}/rto/${rtoCode}`,
        method: "POST",
      }),
      invalidatesTags: [
        "Admin",
        { type: "Qualifications", id: "LIST" },
        { type: "RTOs", id: "LIST" }
      ],
    }),
    removeRTOFromQualification: builder.mutation({
      query: ({ qualificationId, rtoCode }) => ({
        url: `/qualification/${qualificationId}/rto/${rtoCode}`,
        method: "DELETE",
      }),
      invalidatesTags: [
        "Admin",
        { type: "Qualifications", id: "LIST" },
        { type: "RTOs", id: "LIST" }
      ],
    }),
  }),
});

export const {
  useGetCompanyDashboardQuery,
  useGetCompanyDashboardThisMonthQuery,
  useGetCompanyDashboardThisYearQuery,
  useGetAllEmployeeQuery,
  useGetAllAgentsQuery,
  useGetAllApplicationQuery,
  useUpdateWeeklyTargetMutation,
  useAddQualificationMutation,
  useEditQualificationMutation,
  useBulkImportQualificationsMutation,
  useDeleteQualificationMutation,
  useRegisterAdminMutation,
  useRegisterSalesMutation,
  useRegisterOperationsMutation,
  useDeleteEmployeeMutation,
  useUploadXeroInvoiceMutation,
  useGetAllKPI1XeroDataQuery,
  useUploadXeroInBankMutation,
  useGetAllKPI2XeroDataQuery,
  // RTO-Qualification Management hooks
  useAddRTOToQualificationMutation,
  useRemoveRTOFromQualificationMutation,
} = AdminAPIService;
