package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class LeadFilterCriteria {
    // Pagination
    private int page = 0;
    private int size = 100;
    
    // Sorting
    private String sortField;
    private String sortDirection = "desc"; // asc or desc
    
    // Search and filters
    private String search;
    private String agentFilter;
    private String statusFilter;
    private String applicationFilter; // all, hasApplications, noApplications
    private String leadTypeFilter; // all, B2B, Direct
    
    // Date filtering
    private String dateFilterType; // all, today, thisWeek, thisMonth, thisYear, custom, specificDate
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime specificDate;
    
    // For export with selected leads
    private List<String> selectedLeads;
}
