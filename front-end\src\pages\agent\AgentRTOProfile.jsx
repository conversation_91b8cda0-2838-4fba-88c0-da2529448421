import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faExternalLinkAlt,
  faBuilding,
  faMapMarkerAlt,
  faIdCard,
  faInfoCircle,
  faLink,
  faUsers,
  faPhone,
  faEnvelope,
  faBriefcase
} from "@fortawesome/free-solid-svg-icons";
import {
  useGetRTOByCodeQuery,
} from "../../services/CompanyAPIService";
import LoadingSpinner from "../../components/common/LoadingSpinner";

const AgentRTOProfile = () => {
  const { rtoCode } = useParams();
  const navigate = useNavigate();

  // Fetch RTO data
  const {
    data: rto,
    isLoading,
    error,
  } = useGetRTOByCodeQuery(rtoCode, {
    skip: !rtoCode,
  });

  const handleExternalLink = () => {
    if (rto?.code) {
      window.open(`https://training.gov.au/Organisation/Details/${rto.code}`, '_blank');
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Error Loading RTO</h2>
          <p className="text-gray-600 mb-4">
            There was an error loading the RTO information. Please try again later.
          </p>
          <button
            onClick={() => navigate("/agent/qualifications")}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            Back to Qualifications
          </button>
        </div>
      </div>
    );
  }

  if (!rto) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">RTO Not Found</h2>
          <p className="text-gray-600 mb-4">
            The RTO with code {rtoCode} could not be found. It may have been deleted or the code is incorrect.
          </p>
          <button
            onClick={() => navigate("/agent/qualifications")}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            Back to Qualifications
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center">
            <button
              onClick={() => navigate("/agent/qualifications")}
              className="mr-4 text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faArrowLeft} size="lg" />
            </button>
            <div>
              <h1 className="text-xl font-bold text-gray-800">{rto.legalName}</h1>
              <div className="flex items-center mt-1">
                <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded text-sm font-medium">
                  {rto.code}
                </span>
                {rto.rtoType && (
                  <span className="ml-2 text-gray-500 text-sm">
                    {rto.rtoType}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleExternalLink}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-2" />
              View on TGA
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex items-center mb-4">
          <FontAwesomeIcon icon={faUsers} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-bold text-gray-800">Contact Summary</h2>
        </div>
        <div className="flex items-center bg-blue-50 p-4 rounded-lg">
          <div className="bg-blue-100 p-3 rounded-full mr-4">
            <FontAwesomeIcon icon={faUsers} className="text-blue-600" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Total Contacts</div>
            <div className="text-2xl font-bold text-gray-800">{rto.contacts?.length || 0}</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* RTO Information */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800 flex items-center">
                <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-blue-600" />
                RTO Information
              </h2>
              <button
                onClick={handleExternalLink}
                className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <FontAwesomeIcon icon={faLink} className="mr-1" />
                TGA Link
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-600 mb-1">RTO Code</label>
                    <div className="flex items-center bg-gray-50 p-3 rounded">
                      <FontAwesomeIcon icon={faIdCard} className="mr-2 text-blue-600" />
                      <span className="text-gray-800 font-medium">{rto.code}</span>
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-600 mb-1">Legal Name</label>
                    <div className="flex items-center bg-gray-50 p-3 rounded">
                      <FontAwesomeIcon icon={faBuilding} className="mr-2 text-blue-600" />
                      <span className="text-gray-800">{rto.legalName}</span>
                    </div>
                  </div>
                  {rto.businessName && rto.businessName !== rto.legalName && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-600 mb-1">Business Name</label>
                      <div className="flex items-center bg-gray-50 p-3 rounded">
                        <FontAwesomeIcon icon={faBuilding} className="mr-2 text-blue-600" />
                        <span className="text-gray-800">{rto.businessName}</span>
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-600 mb-1">RTO Type</label>
                    <div className="flex items-center bg-gray-50 p-3 rounded">
                      <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-blue-600" />
                      <span className="text-gray-800">{rto.rtoType || "Registered Training Organisation"}</span>
                    </div>
                  </div>
                  {rto.address && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-600 mb-1">Address</label>
                      <div className="flex items-start bg-gray-50 p-3 rounded">
                        <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2 mt-1 text-blue-600" />
                        <span className="text-gray-800">{rto.address}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
              <FontAwesomeIcon icon={faLink} className="mr-2 text-blue-600" />
              Quick Links
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleExternalLink}
                className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 p-3 rounded-lg text-left flex items-center transition-colors duration-200"
              >
                <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-3" />
                <div>
                  <div className="font-medium">Training.gov.au</div>
                  <div className="text-sm text-blue-600">View official RTO page</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contacts Section */}
      {rto.contacts && rto.contacts.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200 px-6 py-4">
            <h2 className="text-lg font-bold text-gray-800 flex items-center">
              <FontAwesomeIcon icon={faUsers} className="mr-2 text-blue-600" />
              RTO Contacts
            </h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {rto.contacts.map((contact, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <FontAwesomeIcon icon={faUsers} className="text-blue-600 mr-2" />
                    <h4 className="font-medium text-gray-800">
                      {contact.firstName} {contact.lastName}
                    </h4>
                  </div>
                  {contact.jobTitle && (
                    <div className="flex items-center mb-1 text-sm text-gray-600">
                      <FontAwesomeIcon icon={faBriefcase} className="mr-2 w-4" />
                      {contact.jobTitle}
                    </div>
                  )}
                  {contact.phone && (
                    <div className="flex items-center mb-1 text-sm text-gray-600">
                      <FontAwesomeIcon icon={faPhone} className="mr-2 w-4" />
                      {contact.phone}
                    </div>
                  )}
                  {contact.email && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FontAwesomeIcon icon={faEnvelope} className="mr-2 w-4" />
                      {contact.email}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentRTOProfile;
