import React from "react";
import { FaSort, FaSortUp, FaSortDown } from "react-icons/fa";
import LeadTableRow from "./LeadTableRow";

const LeadTable = ({
  leads,
  onProfileRedirect,
  onStatusChange,
  onEditLead,
  onOpenDrawer,
  onDeleteLead,
  sortField,
  sortDirection,
  onSort,
  isAdmin = false,
  isLoading = false,
  isUpdatingStatus = false
}) => {
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="ml-1 text-gray-400" />;
    return sortDirection === 'asc' ?
      <FaSortUp className="ml-1 text-[#6E39CB]" /> :
      <FaSortDown className="ml-1 text-[#6E39CB]" />;
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-50">
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6E39CB] mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading leads...</p>
        </div>
      </div>
    );
  }

  if (leads.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-50">
        <div className="p-8 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-gray-500">No leads found matching your criteria.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full divide-y divide-gray-200">
        <thead>
          <tr className="bg-[#F4F5F9]">
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
              onClick={() => onSort('companyName')}
            >
              <div className="flex items-center">
                Company Name
                {getSortIcon('companyName')}
              </div>
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
              onClick={() => onSort('leadName')}
            >
              <div className="flex items-center">
                Name
                {getSortIcon('leadName')}
              </div>
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
              onClick={() => onSort('phone')}
            >
              <div className="flex items-center">
                Phone
                {getSortIcon('phone')}
              </div>
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
              onClick={() => onSort('email')}
            >
              <div className="flex items-center">
                Email
                {getSortIcon('email')}
              </div>
            </th>
            <th
              className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
              onClick={() => onSort('createdDate')}
            >
              <div className="flex items-center">
                Created At
                {getSortIcon('createdDate')}
              </div>
            </th>
            {/* Conditional column rendering based on user role */}
            {isAdmin ? (
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Assigned Agent
              </th>
            ) : (
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Comment
              </th>
            )}

            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Apps
            </th>

            <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
              Action
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100">
          {leads.map((lead, index) => (
            <LeadTableRow
              key={lead.phone || index}
              lead={lead}
              onProfileRedirect={onProfileRedirect}
              onStatusChange={onStatusChange}
              onEditLead={onEditLead}
              onOpenDrawer={onOpenDrawer}
              onDeleteLead={onDeleteLead}
              isAdmin={isAdmin}
              isUpdatingStatus={isUpdatingStatus}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default LeadTable;
